//Add MONGO_URL=mongodb://127.0.0.1:27017/wify_tms in .env file

const envFileSuffix = process.env.NODE_ENV ? '.' + process.env.NODE_ENV : '';
require('dotenv').config(
    // { path: './.env' + (process.env.NODE_ENV == "production") ? ".production" : '' }
    { path: `./.env${envFileSuffix}` }
);
const os = require('os');
const crypto = require('crypto');
var createError = require('http-errors');
var express = require('express');
var path = require('path');
var logger = require('morgan');
var massive = require('massive');
const { MongoClient } = require('mongodb');
const { Client: pgClient } = require('pg');
const { migrate } = require('postgres-migrations'); // For DB migrations
var cors = require('cors');
const bodyParser = require('body-parser');
const Queue = require('bull');
var machineHash = crypto
    .createHash('md5')
    .update(os.hostname())
    .digest('binary'); // Math.random() ;
var app = express();
var MACHINE_ID = 'TMS_APP_SERVER_' + os.hostname();
app.set('MACHINE_ID', MACHINE_ID);
var connectionString =
    process.env.DB_URL +
    '?' +
    new URLSearchParams({
        application_name: 'TMS_APP_SERVER_' + os.hostname(),
    }).toString();
var connectionStringReplica =
    process.env.DB_URL_REPLICA +
    '?' +
    new URLSearchParams({
        application_name: 'TMS_APP_SERVER_' + os.hostname(),
    }).toString();
var mongoConnectionString = process.env.MONGO_URL;

//--------------------< Route Map >----------------------//
// Register your modules here
const routeMap = require('./routes/map');
const {
    kibanaProxyProcessor,
} = require('./api_models/proxys/kibanaProxyProcessor');

const { allQueues } = require('./api_models/queues_v2/queues');
const appDir = path.dirname(require.main.path);
const bull_redis = process.env.BULL_REDIS_URL || 'redis://localhost:6379';

//--------------------< Essentials >----------------------//
//--------------------------------------------------------//
setupEssentials = () => {
    // view engine setup
    app.use(cors());
    app.use(bodyParser.json({ limit: process.env.POST_BODY_SIZE_LIMIT }));
    app.use(
        bodyParser.urlencoded({
            limit: process.env.POST_BODY_SIZE_LIMIT,
            extended: true,
            parameterLimit: 50000,
        })
    );
    app.set('views', path.join(__dirname, 'views'));
    app.set('view engine', 'jade');
    app.use(logger('dev'));
    app.use(express.json());
    app.use(express.urlencoded({ extended: false }));
    //NOTE: we deal with JWT, hence cookie not needed
    // app.use(cookieParser());
    app.use(express.static(path.join(__dirname, 'public')));
    console.log('Essentials init done 👍');
};

//--------------------< Routes >----------------------//
//--------------------------------------------------------//
setupRoutes = () => {
    for (let i = 0; i < routeMap.length; i++) {
        const R = routeMap[i];
        // console.log(`ROUTEEEEE>>>>>`,R);
        console.log(`Initializing ${R.route_file} --> ${R.path}`);
        const router = require(R.route_file);
        if (R.middleware && R.middleware.length > 0) {
            app.use(R.path, ...[...R.middleware, router]);
        }
        app.use(R.path, router);
        console.log(`👍`);
    }
};

//------------------< Error Handlers >--------------------//
//--------------------------------------------------------//
setupErrorHandlers = () => {
    // catch 404 and forward to error handler
    app.use(function (req, res, next) {
        next(createError(404));
    });

    // error handler
    app.use(function (err, req, res, next) {
        // set locals, only providing error in development
        res.locals.message = err.message;
        res.locals.error = req.app.get('env') === 'development' ? err : {};
        // render the error page
        res.status(err.status || 500);
        res.render('error');
    });

    console.log('Error handlers setup done 👍');
};

//------------------< Error Handlers >--------------------//
//--------------------------------------------------------//

// setup migrations
setupMigrations = (db) => {
    const client = new pgClient(connectionString);
    let migrationsDirectory = path.join(__dirname, 'migrations');
    client
        .connect()
        .then(() => {
            // pg must be connected now
            // console.log('PG connected',client);
            migrate({ client }, migrationsDirectory)
                .then((migrations) => {
                    console.info('completed migrations - ', migrations);
                    connectToMassiveAndStart();
                    setTimeout(() => {
                        connectToDBReplicaAndStart();
                    }, 1000);
                })
                .catch((err) => console.error(err));
        })
        .catch((e) => {
            console.log(e);
        });
};

setupMigrations();

// Setup kibana proxy
initKibanaProxy = () => {
    var http = require('http');
    var httpProxy = require('http-proxy');
    var proxy = httpProxy.createProxyServer({});
    http.createServer(function (req, res) {
        if (kibanaProxyProcessor(req, res))
            proxy.web(req, res, { target: process.env.KIBANA_URL });
    }).listen(process.env.KIBANA_PROXY_PORT, (err) => {
        if (err) {
            console.log('Kibana proxy failed to start');
        } else {
            console.log(
                `Initialised... kibana proxy:${process.env.KIBANA_PROXY_PORT} 👍`
            );
        }
    });
};

setupQueues = () => {
    console.log('Setting up queues');
    const proccessorsDirPath = appDir + '/api_models/queues_v2/processors';
    Object.keys(allQueues).forEach((queueIdentifier) => {
        const singleQueueDetails = allQueues[queueIdentifier];
        const newQueue = new Queue(queueIdentifier, bull_redis);
        if (singleQueueDetails.ons) {
            Object.keys(singleQueueDetails.ons).forEach((on) => {
                newQueue.on(on, singleQueueDetails.ons[on]);
            });
        }
        const processorFunction = require(
            proccessorsDirPath + singleQueueDetails.processor
        ).default;
        newQueue.process((job, done) => {
            processorFunction(job, done);
        });
        app.set(`Queue_${queueIdentifier}`, newQueue);
        const addJobFunction = (job, options) => {
            options = {
                ...options,
                attempts: 2,
                removeOnComplete: true,
                removeOnFail: true,
            };
            app.get(`Queue_${queueIdentifier}`).add(job, options);
        };
        app.set(`Queue_${queueIdentifier}_ADD`, addJobFunction);
        console.log(`Initialised queue - ${queueIdentifier} 👍`);
    });
    console.log('Setting up queues - Complete');
};

// Essential: we ensure that the DB/storage connection is established
//----------------< App Initialization >------------------//
//--------------------------------------------------------//

connectToMassiveAndStart = () => {
    massive(connectionString)
        .then((massiveInstance) => {
            console.log('Database connection is established 👍');
            app.set(`db`, massiveInstance);
            app.set('start_time', new Date());

            setupEssentials();

            setupRoutes();

            setupErrorHandlers();

            initKibanaProxy();

            connectToMongo();

            console.log(
                '\nWe are listening for requests... 📡 📡 📡 at ' +
                    `http://localhost:${process.env.PORT}`
            );
        })
        .catch((e) => {
            console.log(e);
        });
};

connectToDBReplicaAndStart = () => {
    if (process.env.DB_URL_REPLICA) {
        massive(connectionStringReplica)
            .then((massiveReplicaInstance) => {
                console.log('Replica Database connection is established 👍');
                app.set(`db_replica`, massiveReplicaInstance);
            })
            .catch((e) => {
                console.log(e);
            });
    } else {
        console.log('Missing ENV variable -> DB_URL_REPLICA');
    }
};

connectToMongo = async () => {
    const client = new MongoClient(mongoConnectionString);
    try {
        // Connect to the MongoDB cluster
        await client.connect();
        console.log('Connected to mongo 👍');
        app.set('mongo_db', await client.db());
    } catch (e) {
        console.log('Error connecting to mongo', e);
        // console.error(e);
    }
};

module.exports = app;
