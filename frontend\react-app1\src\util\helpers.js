import React from 'react';
import { DatePicker, message, Rate, Empty, Button } from 'antd';
import { isArray } from 'lodash';
import moment from 'moment';
import { v4 as uuidv4 } from 'uuid';
import ConfigHelpers from './ConfigHelpers';
import * as XLSX from 'xlsx';
import { decodeFileSectionsFrmJson } from '../components/wify-utils/FieldCreator/helpers';
import { isInsideMobileApp } from './AppHelpers';
import LocationSearchInput from '../components/LocationSearchInput';
import {
    addressFill,
    getAddressBasedOnLatAndLng,
    getAddressFieldKeys,
    getConcatenatedAddressFrmForm,
} from './CustomerHelpers';
import {
    getAddressObj,
    getConcatenatedAddressFormData,
} from '../routes/users/helper';
import MapComponent from '../components/wify-utils/MapComponent/index ';
import { useLocation } from 'react-router-dom';

const _ = require('lodash');
const fileExtensionRegex = /(?:\.([^.]+))?$/;
// Common render function
export const renderCellContent = (text, item, statuses, backgroundClass) => {
    const shouldApplyBackground =
        text !== 0 && !isKeyInDoneOrClosedStatuses(item.status_key, statuses);
    const className =
        text !== 0
            ? `table_zero ${shouldApplyBackground ? backgroundClass : ''}`
            : '';
    return <span className={className}> {text} </span>;
};

// Utility function to check if a key is in DONE or CLOSED statuses
export const isKeyInDoneOrClosedStatuses = (key, srvc_type_statuses) => {
    let statuses;
    try {
        statuses = JSON.parse(srvc_type_statuses);
    } catch (error) {
        console.error('Failed to parse statuses JSON:', error);
        return false;
    }
    const doneKeys = statuses?.DONE?.map((status) => status.key);
    const closedKeys = statuses?.CLOSED?.map((status) => status.key);
    return doneKeys?.includes(key) || closedKeys?.includes(key);
};

export const calculateCenterLatLng = (latlngArray) => {
    if (latlngArray.length === 0) {
        return null;
    }
    var sumLat = 0;
    var sumLng = 0;

    // Calculate the sum of all latitudes and longitudes
    for (var i = 0; i < latlngArray.length; i++) {
        sumLat += latlngArray[i][0]; // Latitude
        sumLng += latlngArray[i][1]; // Longitude
    }
    // Calculate the average of latitudes and longitudes
    var avgLat = sumLat / latlngArray.length;
    var avgLng = sumLng / latlngArray.length;

    return [avgLat, avgLng]; // Return the center latitude and longitude as an array
};

export const convertTimeToSortableFormat = (timeString) => {
    const [time, period] = timeString.split(/(?=[AP]M)/i); // Split time and period (AM/PM)
    let [hours, minutes] = time.split(':'); // Split hours and minutes
    hours = parseInt(hours, 10); // Parse hours as integer
    if (period && period.toLowerCase() === 'pm' && hours < 12) {
        hours += 12; // Add 12 hours for PM time
    } else if (period && period.toLowerCase() === 'am' && hours === 12) {
        hours = 0; // Convert 12 AM to 0 hours
    }
    return hours * 60 + parseInt(minutes, 10); // Convert to minutes for sorting
};

export function downloadExcelFileWithTabwise(dataWithCategories, fileName) {
    /*
        //Generate and download an Excel file with tab-wise data.
        //Sample data format for downloadExcelFileWithTabwise function:
        const dataWithCategories = [
            {
                "name": "Seating",
                "items": [
                {
                    "Area": "1BHK",
                    "Room": "Hall",
                    "Width": 12
                },
                {
                    "Area": "2BHK",
                    "Room": "Bedroom",
                    "Width": 20
                }
                ]
            },
            {
                "name": "Sqft",
                "items": [
                {
                    "Quantity": 10,
                    "Price": 20,
                    "Total": 200
                }
                ]
            }
        ];
    */
    try {
        const workbook = XLSX.utils.book_new();
        // Loop through each category group
        dataWithCategories.forEach(({ name, items }) => {
            const worksheet = XLSX.utils.json_to_sheet(items);
            XLSX.utils.book_append_sheet(workbook, worksheet, name);
        });
        if (!isInsideMobileApp()) {
            // Generate and download the Excel file
            XLSX.writeFile(workbook, fileName + '.xlsx');
        } else {
            downloadExcelFileForAndroid(workbook, fileName);
        }
    } catch (error) {
        console.error('Error generating and downloading Excel file:', error);
    }
}

export function downloadExcelFileForAndroid(workbook, fileName) {
    window.wifyApp.showToastMessage('Downloading File...');
    // Generate the binary string for the Excel file then create blob url
    const binaryString = XLSX.write(workbook, {
        bookType: 'xlsx',
        type: 'binary',
    });
    const arrayBuffer = s2ab(binaryString);
    const blob = new Blob([arrayBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const blobUrl = URL.createObjectURL(blob);

    // Pass the blob url & base64 data to the mobile interface to handle file saving
    getBase64FromBlobUrl(blobUrl, function (base64data) {
        if (base64data) {
            window.wifyApp.saveWorkbookFileInDevice(
                blobUrl,
                base64data,
                fileName + '.xlsx'
            );
        } else {
            window.wifyApp.showToastMessage('Downloading Failed!');
            console.error('Failed to fetch base64 data');
        }
    });
}

function getBase64FromBlobUrl(blobUrl, callback) {
    fetch(blobUrl)
        .then((response) => response.blob())
        .then((blob) => {
            const reader = new FileReader();
            reader.onloadend = function () {
                const base64data = reader.result.split(',')[1]; // Extract base64 part of the data URL
                callback(base64data);
            };
            reader.readAsDataURL(blob);
        })
        .catch((error) => {
            console.error('Error fetching the blob:', error);
            callback(null); // Return null if there's an error
        });
}

// Convert binary string to ArrayBuffer
function s2ab(s) {
    const buf = new ArrayBuffer(s.length);
    const view = new Uint8Array(buf);
    for (let i = 0; i < s.length; i++) {
        view[i] = s.charCodeAt(i) & 0xff;
    }
    return buf;
}

/* 
    Unit should be
    1000 for thousand
    100000 for lakhs
    .. so on
*/
export function convertValueToUnit(num, unit) {
    if (isNaN(num)) {
        return '';
    }
    const suffixes = {
        K: 1000,
        L: 100000,
        Cr: 10000000,
        // 'Ar': 1000000000,
        // 'Ab': 1000000000000
    };

    if (!suffixes.hasOwnProperty(unit)) {
        return 'Invalid unit';
    }

    let divisor = suffixes[unit];
    let formattedNum = (num / divisor).toFixed(2) + unit;

    return formattedNum;
}

export function getCreatedDateFilter(title, activeFilters, fromDate, toDate) {
    let creation_date = activeFilters.hasOwnProperty('creation_srvc_req_date');
    if (creation_date && activeFilters?.creation_srvc_req_date) {
        fromDate = activeFilters?.creation_srvc_req_date?.[0];
        toDate = activeFilters?.creation_srvc_req_date?.[1];
    }
    var currentDate =
        moment().local().format('MMM-DD-YYYY ') +
        moment()
            .toDate()
            .toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    return (
        <h2 className="h4 gx-text-capitalize gx-ml-3 gx-mb-4">
            <span> {title} </span>
            {creation_date ? (
                <small className="gx-text-muted">
                    ( Created Between {convertUTCToDisplayTime(fromDate, true)}{' '}
                    To {convertUTCToDisplayTime(toDate, true)} )
                </small>
            ) : currentDate ? (
                <small className="gx-text-muted">
                    ( Created between{' '}
                    {convertUTCToDisplayTime(currentDate, true)} To{' '}
                    {getCurrentDateAndTimeFrDisplay()} )
                </small>
            ) : (
                []
            )}
        </h2>
    );
}

export function getParamsObjFromUrl({ paramName = '' }) {
    const urlSearch = document.location.href;
    const [baseUrl, _query] = urlSearch.split('?');
    let _queryRes = {};
    if (paramName && _query && _query.includes(paramName)) {
        _queryRes = convertNestedQueryUrlToObj({ url: _query });
    }
    return _queryRes;
}

export function convertNestedQueryUrlToObj({ url = false }) {
    if (!url) {
        console.log('convertNestedQueryUrlToObj :: No url given');
        return {};
    }
    const params = new URLSearchParams(url);
    const obj = {};

    for (const [key, value] of params.entries()) {
        const decodedKey = decodeURIComponent(key);
        const decodedValue = decodeURIComponent(value);
        try {
            const nestedObj = JSON.parse(decodedValue);
            obj[decodedKey] = nestedObj;
        } catch (error) {
            obj[decodedKey] = decodedValue;
        }
    }
    return obj;
}

export function updateQueryParams({ query = '' }) {
    const urlSearch = document.location.href;
    const [baseUrl, _query] = urlSearch.split('?');
    if (_query) {
        const newUrl = `${urlSearch}&${query}`;
        window.history.replaceState({}, '', newUrl);
    } else {
        const newUrl = `${window.location.pathname}?${query}`;
        window.history.replaceState({}, '', newUrl);
    }
}

export function stringifyQueryParams({ queryObject = {} }) {
    const urlSearch = new URLSearchParams();
    for (const key in queryObject) {
        if (Object.hasOwnProperty.call(queryObject, key)) {
            let _value = queryObject[key];
            if (typeof _value == 'object') {
                _value = JSON.stringify(_value);
            }
            urlSearch.append(key, _value);
        }
    }
    return urlSearch.toString();
}

export const showCreateOrUpdateSuccessMessage = (
    top_ = 200,
    duration_ = 2,
    message_ = 'Submitted successfully'
) => {
    message.config({
        top: top_, // Set the desired distance from the top of the viewport
        duration: duration_, // Set the duration in seconds for the message to be displayed
    });
    message.success(message_);
};

export const getCustomFileFieldsFilter = (srvcCustomFieldsJson) => {
    let customFileFieldsMeta = [];
    let customFileFields = decodeFileSectionsFrmJson(srvcCustomFieldsJson);
    if (customFileFields) {
        customFileFields.forEach((singleCustomFileField) => {
            let customFileFieldObj = {
                key: singleCustomFileField.key + '_uploaded',
                label: singleCustomFileField.title + ' Uploaded',
                placeholder: 'Select..',
                widget: 'select',
                widgetProps: {
                    // "mode":"multiple" ,
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                options: [
                    { label: 'Any', value: '-1', color: 'gx-bg-grey' },
                    { label: 'Yes', value: 'Yes' },
                    { label: 'No', value: 'No' },
                ],
            };
            customFileFieldsMeta.push(customFileFieldObj);
        });
    }
    return customFileFieldsMeta;
};

const priorities = [
    //urgent,high,normal,low, no priority
    { value: 'Urgent', label: 'Urgent', color: '#f5222d' },
    { value: 'High', label: 'High', color: '#fadb14' },
    { value: 'Normal', label: 'Normal', color: '#13c2c2' },
    { value: 'Low', label: 'Low', color: '#52c41a' },
    { value: 'None', label: 'None', color: '#8c8c8c' },
];

export const BILLING_TYPE = [
    { value: 'line_item', label: 'Line item' },
    { value: 'manday', label: 'Manday' },
    { value: 'hybrid', label: 'Hybrid' },
    { value: 'foc', label: 'FOC' },
];

export const getSelectOptionsFrZeroToHundred = (startVal = 1) => {
    let returnValue = [];
    for (var i = startVal; i <= 100; i++) {
        returnValue.push({ value: i, label: i });
    }
    return returnValue;
};

export const BILLING_DISCOUNT_TYPE = [
    { value: 'percentage', label: 'Percentage' },
    { value: 'value', label: 'Value' },
];

export const BILLING_APPROVER_TYPE = [
    { value: 'authority_based', label: 'Authority based' },
    { value: 'static_user', label: 'Static User' },
];

export const PROJECT_BASED_SERVICE_TYPE = 'project_based';

export const getOptionsFrNatureOfServiceType = () => {
    return [
        { label: 'Task based', value: 'task_based' },
        { label: 'Project based', value: PROJECT_BASED_SERVICE_TYPE },
    ];
};
export const getOptionsFrDefaultModeOfRating = () => {
    return [
        { label: 'Taskwise', value: 'task_wise' },
        { label: 'Daywise', value: 'day_wise' },
    ];
};

export const getValueDataFrmFormMeta = (fieldsMeta, form_data) => {
    //Remove dublicate key from json
    fieldsMeta = fieldsMeta.filter(
        (listItem, index, self) =>
            self.map((itm) => itm.key).indexOf(listItem.key) === index
    );

    let valueData = {};
    fieldsMeta.forEach((singleFieldMeta) => {
        let key = singleFieldMeta?.key;
        let value = form_data?.[key];
        if (form_data?.[key]) {
            delete singleFieldMeta['colSpan'];
            if (singleFieldMeta.widget == 'date-picker') {
                value = moment.utc(value).format('MMM Do YYYY');
            } else if (singleFieldMeta.widget == 'select') {
                value = getLabelFrmOptionsValue(singleFieldMeta.options, value);
            } else if (singleFieldMeta.widget?.displayName == 'Rate') {
                value = getLabelFrmOptionsValue(singleFieldMeta.options, value);
            } else if (singleFieldMeta.widget == 'radio-group') {
                value = getLabelFrmOptionsValue(singleFieldMeta.options, value);
            } else if (singleFieldMeta.widget == 'checkbox-group') {
                value = getLabelFrmOptionsValue(singleFieldMeta.options, value);
            } else if (singleFieldMeta.widget) {
                let currValue = form_data[key]; // check if label in value
                if (currValue.label) {
                    value = currValue.label;
                }
            }
        }
        valueData[key] = value;
    });
    return valueData;
};

export const parseFormulaToString = (
    formula,
    nameToIdMapping = undefined,
    data
) => {
    // debugger;
    let matches = formula.match(/{[^{}]+}/g);
    matches.forEach((matchString) => {
        let word = matchString.substring(1, matchString.length - 1);
        let key = nameToIdMapping[word];
        let fieldValue = data[key] || 'N/A';
        formula = formula.replace(`{${word}}`, fieldValue);
    });
    return formula;
};

export const parseFormulaToValue = (
    formula,
    nameToIdMapping = undefined,
    data,
    isSrvcReqLocked = true
) => {
    let matches = formula.match(/{[^{}]+}/g);
    matches.forEach((matchString) => {
        let word = matchString.substring(1, matchString.length - 1);
        let key = nameToIdMapping[word];
        let fieldValue = key in data ? data[key] : 'undefined';
        formula = formula.replace(`{${word}}`, fieldValue);
    });
    let result = 0;
    try {
        result = eval(formula);
        if (!isSrvcReqLocked) {
            result = parseFloat(result).toFixed(2);
        }
    } catch (error) {
        console.log('parseFormulaToValue error', error);
    }
    return result;
};

export const getStringToArray = (data) => {
    let value = [];
    if (data != undefined) {
        if (Object.keys(data).length == 0) {
            value = value;
        } else if (typeof data === 'string') {
            value = data.split(',');
        } else {
            value = data;
        }
        value = value?.filter((singlePin) => singlePin != '');
    }
    return value;
};

/** FROM DB */
export const getStatusListForOptions = (statuses_db) => {
    let decodeStatuses = [];
    statuses_db.map((status) => {
        decodeStatuses.push({
            label: status.title,
            value: status.key,
            color: status.color,
        });
    });
    return decodeStatuses;
};

export const getCenterLocFrIndMap = () => {
    return { lat: 21.7372075, lng: 79.8332191 };
};

const getColorCodeFrStatusCategory = (category) => {
    var colorFrCategory = {
        ACTIVE: '#f44336',
        DONE: '#009688',
        CLOSED: '#4caf50',
    };
    return colorFrCategory[category] ? colorFrCategory[category] : '#e1e1e1';
};
const getTextColorFrPriority = (priority) => {
    var colorFrPriority = {
        Urgent: 'gx-text-red',
        High: 'gx-text-yellow',
        Normal: 'gx-text-cyan',
        Low: 'gx-text-green',
        None: 'gx-text-grey',
    };
    return colorFrPriority[priority] ? colorFrPriority[priority] : '';
};
const getRandomIconByColor = () => {
    var arrayOfColors = [
        'gx-text-green',
        'gx-text-orange',
        'gx-text-purple',
        'gx-text-amber',
        'gx-text-light-blue',
        'gx-text-blue',
        'gx-text-cyan',
        'gx-text-sepia',
        'gx-text-pink',
    ];
    return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];
};

const getRandomBgColor = () => {
    var arrayOfColors = [
        'gx-bg-green',
        'gx-bg-orange',
        'gx-bg-purple',
        'gx-bg-amber',
        'gx-bg-light-blue',
        'gx-bg-blue',
        'gx-bg-cyan',
        'gx-bg-sepia',
        'gx-bg-pink',
    ];
    return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];
};
export const getRandomTagBgColor = () => {
    var arrayOfColors = [
        'magenta',
        'red',
        'volcano',
        'orange',
        'gold',
        'lime',
        'green',
        'cyan',
        'blue',
        'geekblue',
        'purple',
    ];
    return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];
};

const generateRandomKey = (length = 8) => {
    var result = [];
    var characters =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
        result.push(
            characters.charAt(Math.floor(Math.random() * charactersLength))
        );
    }
    return result.join('');
};

const getAnyObjectFrFilter = (text = 'Any') => {
    return { title: text, label: text, value: '-1', color: '#E1E1E1' };
};

const getEmptyObjectFrFilter = (text = 'Empty') => {
    return { title: text, label: text, value: '-2', color: '#E1E1E1' };
};

export const getPriorityObjectFrFilter = () => {
    return {
        key: 'priority',
        label: 'Priority',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
            showSearch: true,
        },
        options: priorities,
    };
};

export const getSpareOptions = () => {
    return [
        { label: 'Repairable', value: 'repairable' },
        { label: 'Non Repairable', value: 'non_repairable' },
    ];
};

export const getInventoryTypeList = () => {
    return [
        { label: 'Product Serialized', value: 'serialized-product' },
        { label: 'Product Non-Serialized', value: 'product' },
        { label: 'Spare Serialized', value: 'serialized-spare' },
        { label: 'Spare Non-Serialized', value: 'spare' },
    ];
};

export const getInventoryType = (inventoryType) => {
    const types = {
        product: {
            stock_item_type: 'non-serialized',
            stock_type: 'product',
            label: 'Product',
        },
        'serialized-product': {
            stock_item_type: 'serialized',
            stock_type: 'product',
            label: 'Product',
        },
        spare: {
            stock_item_type: 'non-serialized',
            stock_type: 'spare',
            label: 'Spare',
        },
        'serialized-spare': {
            stock_item_type: 'serialized',
            stock_type: 'spare',
            label: 'Spare',
        },
    };

    return types[inventoryType] || {};
};

export const getSolidUnitOptions = () => {
    return [
        { label: 'gram', value: 'gram' },
        { label: 'kilogram', value: 'kilogram' },
        { label: 'tonne', value: 'tonne' },
    ];
};

export const getLiquidUnitOptions = () => {
    return [
        { label: 'litre', value: 'litre' },
        { label: 'millilitre', value: 'millilitre' },
    ];
};

export const getUnitOptions = () => {
    return [
        { label: 'Quantity', shortLabel: 'Quantity', value: 'quantity' },
        { label: 'Gram', shortLabel: 'g', value: 'gram' },
        { label: 'Kilogram', shortLabel: 'kg', value: 'kilogram' },
        { label: 'Tonne', shortLabel: 't', value: 'tonne' },
        { label: 'Litre', shortLabel: 'l', value: 'litre' },
        { label: 'Millilitre', shortLabel: 'ml', value: 'millilitre' },
    ];
};

export const getIsDeletedFrFilter = () => {
    return {
        key: 'is_deleted',
        label: 'Show Deleted',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        noAny: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [
            { label: 'Yes', value: true },
            { label: 'No', value: false },
        ],
    };
};

export const getShowPincodeFrFilter = () => {
    return {
        key: 'is_pincode',
        label: 'Pincode Available',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [
            { label: 'Any', value: '-1', color: 'gx-bg-grey' },
            { label: 'Yes', value: true },
            { label: 'No', value: false },
        ],
    };
};
export const getAgingFiltersFrBrand = () => {
    return [
        {
            key: 'stages',
            label: 'Ageing - Status',
            widget: 'select',
            options: [],
        },
        {
            key: 'days_spent',
            label: 'Ageing - Days Spend',
            widget: 'select',
            widgetProps: { mode: 'multiple', optionFilterProp: 'children' },
            options: [],
            noAny: true,
        },
        {
            key: 'show_all',
            label: 'Ageing - Show All',
            widget: 'select',
            options: [
                { value: true, label: 'Yes' },
                { value: false, label: 'No' },
            ],
            noAny: true,
        },
    ];
};
export const getNoOfTasksObjectFrFilter = () => {
    let numberOfTasks = 5;
    let possibleTasksOptions = [];
    possibleTasksOptions = getOptionsListFrPossibleNumbers(
        numberOfTasks,
        (number) => ({
            color: getColorInBetweenByPercentage(
                [255, 72, 0], // Min color
                [0, 250, 156], // Max color
                number / numberOfTasks
            ),
        })
    ).filter((item) => item.value != 0);

    let taskOptionGreaterThanFiveObj = {
        label: '> 5',
        value: '>5',
        color: 'rgb(255,0,0)',
    };
    possibleTasksOptions.push(taskOptionGreaterThanFiveObj);
    return {
        key: 'no_of_tasks',
        label: 'No Of Tasks',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
            showSearch: true,
        },
        options: possibleTasksOptions,
    };
};

const convertDateFieldsToMoments = (form_data, fieldsMeta = undefined) => {
    let returnData = form_data;
    if (returnData != undefined) {
        Object.keys(form_data).map((key) => {
            var fieldValue = form_data[key];
            if (fieldsMeta) {
                let matchingMeta = fieldsMeta.filter(
                    (fieldMeta) => fieldMeta.key == key
                );
                if (matchingMeta.length > 0) {
                    let fieldMeta = matchingMeta[0];
                    if (
                        fieldMeta.widget &&
                        typeof fieldMeta.widget === 'function' &&
                        fieldMeta.widget.name == DatePicker.RangePicker.name
                    ) {
                        //
                        if (
                            isArray(fieldValue) &&
                            fieldValue.length == 2 &&
                            fieldMeta.widgetProps?.ranges
                        ) {
                            returnData[key] = [
                                moment(fieldValue[0], true),
                                moment(fieldValue[1], true),
                            ];
                        }
                    } else if (fieldMeta.widget == 'date-picker') {
                        // console.log('value',value);
                        if (fieldValue && fieldValue != '') {
                            var date = moment(fieldValue, true);
                            if (date.isValid()) {
                                // console.log('Valid date field converted to moment',key,date)
                                returnData[key] = date;
                            } else {
                                returnData[key] = moment();
                            }
                        } else {
                            returnData[key] = undefined;
                        }
                    }
                }
            } else {
                // Legacy
                if (typeof fieldValue === 'string') {
                    var date = moment(fieldValue, true);
                    if (date.isValid()) {
                        // console.log('Valid date field converted to moment',key,date)
                        returnData[key] = date;
                    }
                }
            }
        });
    }
    return returnData;
};

const isTimePassed = (sql_timestamp) => {
    // console.log(sql_timestamp);
    let returnData = false;
    if (sql_timestamp != undefined) {
        var momentUtc = moment.utc(sql_timestamp);
        // console.log(moment.utc(),moment.utc(sql_timestamp));
        if (momentUtc.valueOf() < moment().utc().valueOf()) {
            returnData = true;
        }
    }
    return returnData;
};
export const convertMomentToLocalDateString = (moment_date) => {
    return moment_date?.local().format('MMM-DD-YYYY ');
};

export const getCustomPrefixNameFrUploadingFiles = () => {
    let userName = ConfigHelpers.getFullUserName();
    let currentDate = convertMomentToDateString(moment.utc());
    return `${userName}_${currentDate}`;
};

export const convertMomentToDateString = (moment_date) => {
    return moment_date?.local().format('YYYY-MM-DD');
};

export const getCurrentDay = () => {
    var date = moment();
    return date.local().format('YYYY-MM-DD');
};

export const getCurrentDateAndTimeFrDisplay = () => {
    var date = moment();
    return (
        date.local().format('MMM-DD-YYYY ') +
        date
            .toDate()
            .toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    );
};

const convertUTCToDisplayTime = (sql_timestamp, just_date, just_time) => {
    let returnData = '';
    if (sql_timestamp != undefined) {
        if (typeof sql_timestamp === 'string') {
            var date = moment.utc(sql_timestamp);
            if (date.isValid()) {
                if (just_date) {
                    returnData = date.local().format('MMM-DD-YYYY ');
                } else if (just_time) {
                    returnData = date.local().format('h:mm A');
                } else {
                    returnData =
                        date.local().format('MMM-DD-YYYY ') +
                        date.toDate().toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit',
                        });
                }
            }
        }
    }
    return returnData;
};

const getTouchedFieldsValueInForm = (
    form_data,
    formInstance,
    touchFieldsKeys = []
) => {
    let returnFields = {};
    if (formInstance) {
        let fieldKeys = Object.keys(form_data);
        fieldKeys.forEach((singleKey) => {
            if (
                formInstance.isFieldTouched(singleKey) ||
                touchFieldsKeys.includes(singleKey)
            ) {
                returnFields[singleKey] = form_data[singleKey];
                const fieldValue = form_data[singleKey];
                if (fieldValue === undefined) {
                    // If the field value is undefined, check if it's a touched field
                    if (touchFieldsKeys.includes(singleKey)) {
                        returnFields[singleKey] = null; // Explicitly set to null to ensure it's included in the update
                    }
                }
            }
        });
    } else {
        returnFields = form_data;
        console.log(
            'getTouchedFieldsValueInForm error',
            'Form instance undefined, returning all fields'
        );
    }
    return returnFields;
};

function durationAsString(start) {
    const duration = moment.duration(moment.utc().diff(moment.utc(start)));

    //Get Days
    const days = Math.floor(duration.asDays()); // .asDays returns float but we are interested in full days only
    const daysFormatted = days ? `${days}d ` : ''; // if no full days then do not display it at all

    //Get Hours
    const hours = duration.hours();
    const hoursFormatted = hours > 0 ? `${hours}h ` : '';

    //Get Minutes
    const minutes = duration.minutes();
    const minutesFormatted = `${minutes}m`;

    return [daysFormatted, hoursFormatted, minutesFormatted].join('');
}

// console.log(generateRandomKey(5));
function getLinktoObject(url, params) {
    let search = '';
    let urlSearch = new URLSearchParams();
    for (const key in params) {
        if (Object.hasOwnProperty.call(params, key)) {
            const element = params[key];
            let value = element;
            if (typeof element == 'object') {
                value = JSON.stringify(element);
            }
            urlSearch.append(key, value);
        }
    }
    return { pathname: url, search: urlSearch.toString() };
}

function getGeneralFileSection(required) {
    return { key: 'general', title: 'Attachments', required };
}

function hasAnyFileChanged(newFilesBySection, oldFilesBySection) {
    let hasChanged = false;
    // debugger;
    if (oldFilesBySection == undefined && newFilesBySection) {
        hasChanged = true;
    } else {
        let sections = Object.keys(newFilesBySection);

        sections.map((singleKey) => {
            let newFilesFrSection = newFilesBySection[singleKey];
            let oldFilesFrSection = oldFilesBySection[singleKey];
            if (
                oldFilesFrSection == undefined ||
                newFilesFrSection == undefined
            ) {
                hasChanged = true;
            } else {
                oldFilesFrSection.map((oldFile) => {
                    if (!newFilesFrSection.includes(oldFile)) {
                        hasChanged = true;
                    }
                });
                newFilesFrSection.map((newFile) => {
                    if (!oldFilesFrSection.includes(newFile)) {
                        hasChanged = true;
                    }
                });
            }
        });
    }

    return hasChanged;
}

export const ExcelDateToJSDate = (serial) => {
    var isANumber = isNaN(serial) === false;
    if (isANumber) {
        var utc_days = Math.floor(serial - 25569);
        var utc_value = utc_days * 86400;
        var date_info = new Date(utc_value * 1000);

        var fractional_day = serial - Math.floor(serial) + 0.0000001;

        var total_seconds = Math.floor(86400 * fractional_day);

        var seconds = total_seconds % 60;

        total_seconds -= seconds;

        var hours = Math.floor(total_seconds / (60 * 60));
        var minutes = Math.floor(total_seconds / 60) % 60;

        return new Date(
            date_info.getFullYear(),
            date_info.getMonth(),
            date_info.getDate(),
            hours,
            minutes,
            seconds
        );
    } else {
        // - / and 0 to 9
        var regexp = /[^0-9-\/]+/gi;
        var cleanDateStr = serial?.replace(regexp, '');
        var momentDate = moment(cleanDateStr, 'YYYY-MM-DD', true);
        return momentDate.isValid() ? Date.parse(cleanDateStr) : undefined;
    }
};

export const decodeAntdFormErrorsToString = (errObject) => {
    let finalString = [];
    errObject.errorFields.map(({ errors }) => {
        errors.map((singleError) => {
            finalString.push(singleError);
        });
    });
    return finalString.join(',');
};
export const getLabelFrmOptionsValue = (options, value) => {
    let returnLabel = value;
    if (Array.isArray(options)) {
        if (Array.isArray(value)) {
            // If the value is an array, map over it and get the corresponding labels
            returnLabel = value
                .map((singleValue) => {
                    const matchingOption = options.find(
                        (singleOption) => singleOption.value === singleValue
                    );
                    return matchingOption ? matchingOption.label : singleValue;
                })
                .join(', ');
        } else {
            // If the value is not an array, get the label for the single value
            options.map((singleOption) => {
                if (singleOption.value == value) {
                    returnLabel = singleOption.label;
                }
            });
        }
    }
    return returnLabel;
};

const addDaysToDate = function (date, days) {
    date.setDate(date.getDate() + days);
    return date;
};

export const getDayName = (dateStr, locale) => {
    var date = new Date(dateStr);
    return date.toLocaleDateString(locale, { weekday: 'long' });
};

export const getDaysBetweenMoments = (
    startDate,
    stopDate,
    inclusive = false
) => {
    return getDaysBetweenDates(
        startDate?.toDate(),
        stopDate?.toDate(),
        inclusive
    );
};
export const getDaysBetweenDates = (startDate, stopDate, inclusive = false) => {
    console.log('startDate', startDate, stopDate);
    var dateArray = new Array();
    var currentDate = startDate;
    while (currentDate <= stopDate) {
        dateArray.push(convertMomentToDateString(moment(currentDate)));
        currentDate = addDaysToDate(currentDate, 1);
    }
    if (!inclusive && dateArray.length > 0) {
        dateArray.shift();
        dateArray.pop();
    }
    return dateArray;
};

export const getPresetRangesForFutureSpans = () => {
    return {
        Today: [moment(), moment()],
        Today: [moment(), moment()],
        Tomorrow: [moment().add(1, 'days'), moment().add(1, 'days')],
        'Next 2 days': [moment().add(1, 'days'), moment().add(2, 'days')],
        'Next 3 days': [moment().add(1, 'days'), moment().add(3, 'days')],
        'Next 4 days': [moment().add(1, 'days'), moment().add(4, 'days')],
        'Next 5 days': [moment().add(1, 'days'), moment().add(5, 'days')],
        'Next 6 days': [moment().add(1, 'days'), moment().add(6, 'days')],
        'Next 7 days': [moment().add(1, 'days'), moment().add(7, 'days')],
        'Next 2 weeks': [moment().add(1, 'days'), moment().add(14, 'days')],
        'Next month': [moment().add(1, 'days'), moment().add(31, 'days')],
    };
};

export const getPresetRangesForRangeDatePicker = () => {
    return {
        Today: [moment(), moment()],
        Yesterday: [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 days': [moment().subtract(7, 'days'), moment()],
        'Last week': [
            moment().subtract(1, 'weeks').startOf('week'),
            moment().subtract(1, 'weeks').endOf('week'),
        ],
        'Month to date': [moment().startOf('month'), moment()],
        'Previous Month': [
            moment().subtract(1, 'months').startOf('month'),
            moment().subtract(1, 'months').endOf('month'),
        ],
        'Year to Date': [moment().startOf('year'), moment()],
    };
};

export const setPageTitleAndFavIcon = (title, icon) => {
    const favicon = document.getElementById('favicon');
    if (icon && icon != '') {
        favicon.href = icon;
    }
    const html_title = document.getElementById('html_title');
    if (title && title != '') {
        html_title.innerHTML = title;
    }
};

export const getSearchInLinkAsObject = () => {
    let searchParams = new URLSearchParams(
        document.location.search.substring(1)
    );
    let paramsObject = {};
    searchParams.forEach((value, key) => (paramsObject[key] = value));
    // message.info(JSON.stringify(paramsObject));
    return paramsObject;
};

// http://jsfiddle.net/vksn3yLL/
export const getColorInBetweenByPercentage = (color1, color2, percentage) => {
    var p = percentage;
    var w = p * 2 - 1;
    var w1 = (w / 1 + 1) / 2;
    var w2 = 1 - w1;
    var rgb = [
        Math.round(color1[0] * w1 + color2[0] * w2),
        Math.round(color1[1] * w1 + color2[1] * w2),
        Math.round(color1[2] * w1 + color2[2] * w2),
    ];
    return 'rgb(' + rgb.join() + ')';
};

export const getOptionsListFrPossibleNumbers = (
    maxNumber,
    customParamsCallback
) => {
    let returnOptionsArray = [];
    if (maxNumber) {
        let optionsArray = Array.from(Array(maxNumber + 1).keys());
        optionsArray = optionsArray.map((value, index) => {
            let customParams = {};
            if (customParamsCallback) {
                customParams = customParamsCallback(index);
            }

            return { label: '' + index, value: index, ...customParams };
            console.log('options index', index);
        });
        // console.log("optionsArray",optionsArray)
        returnOptionsArray.push(...optionsArray);
    }
    return returnOptionsArray;
};

export const getViewModeMetaFrFormMeta = (fieldsMeta, form_data) => {
    //Remove dublicate key from json
    fieldsMeta = fieldsMeta.filter(
        (listItem, index, self) =>
            self.map((itm) => itm.key).indexOf(listItem.key) === index
    );

    let formMetaFrDisplay = [];
    fieldsMeta.forEach((singleFieldMeta) => {
        let key = singleFieldMeta.key;
        if (form_data[key]) {
            delete singleFieldMeta['colSpan'];
            if (singleFieldMeta.widget == 'date-picker') {
                singleFieldMeta.renderView = (value) => (
                    <div>{moment.utc(value).format('MMM Do YYYY')}</div>
                );
            } else if (singleFieldMeta.widget == 'select') {
                singleFieldMeta.renderView = (value) => (
                    <span>
                        {getLabelFrmOptionsValue(
                            singleFieldMeta.options,
                            value
                        )}
                    </span>
                );
            } else if (singleFieldMeta.widget?.displayName == 'Rate') {
                singleFieldMeta.renderView = (value) => (
                    <Rate value={(singleFieldMeta.options, value)} disabled />
                );
            } else if (singleFieldMeta.widget == 'radio-group') {
                singleFieldMeta.renderView = (value) => (
                    <span>
                        {getLabelFrmOptionsValue(
                            singleFieldMeta.options,
                            value
                        )}
                    </span>
                );
            } else if (singleFieldMeta.widget == 'checkbox-group') {
                singleFieldMeta.renderView = (value) => (
                    <span>
                        {getLabelFrmOptionsValue(
                            singleFieldMeta.options,
                            value
                        )}
                    </span>
                );
            } else if (singleFieldMeta.widget) {
                let value = form_data[key]; // check if label in value
                if (value.label) {
                    singleFieldMeta.renderView = (value) => (
                        <span>{value.label}</span>
                    );
                }
            } else if (singleFieldMeta.key == 'comment') {
                singleFieldMeta.renderView = (value) => {
                    return value;
                };
            }
            // key exists in form_data so this field needs to be displayed
            formMetaFrDisplay.push(singleFieldMeta);
        }
    });

    return formMetaFrDisplay;
};

export const generateUUID = () => {
    return uuidv4();
};

export const NoData = () => {
    return <Empty data-testid="no-data" image={Empty.PRESENTED_IMAGE_SIMPLE} />;
};

export const getRandomHexBgColor = () => {
    var arrayOfColors = [
        '#008000',
        '#FFA500',
        '#800080',
        '#FFBF00',
        '#ADD8E6',
        '#0000FF',
        '#00FFFF',
        '#704214',
        '#FFC0CB',
    ];
    return arrayOfColors[Math.floor(Math.random() * arrayOfColors.length)];
};

export const getColorCodeFrReqDate = (category) => {
    var colorFrReqDate = {
        TODAY: '#008080',
        UPCOMING: '#FFA500',
        OVERDUE: '#FF0000',
        UNSPECIFIED: '#808080',
    };
    return colorFrReqDate[category] ? colorFrReqDate[category] : '#e1e1e1';
};

export const isAndroidApp = () => {
    const user_agent = navigator?.userAgent;
    console.log('user_agent', user_agent);

    return user_agent.includes('||TMS_APP');
};

export const areArraysSame = (a1, a2) => {
    /* WARNING: arrays must not contain {objects} or behavior may be undefined */
    return JSON.stringify(a1) == JSON.stringify(a2);
};

export const handleClearSelect = (value, formRef, key) => {
    let fieldKey = {};
    fieldKey[key] = '';
    if (value == undefined) {
        if (formRef?.current?.setFieldsValue) {
            formRef.current.setFieldsValue(fieldKey);
        }
    }
};

export const setRemarkFieldAsNoRemarkIfEmpty = (prefillFormData, formRef) => {
    if (formRef?.current) {
        const remark = formRef?.current?.getFieldValue('remarks');
        if (prefillFormData?.remarks == undefined && remark == undefined) {
            formRef.current.setFieldsValue({ remarks: 'No remarks' });
        }
    } else {
        setTimeout(() => {
            setRemarkFieldAsNoRemarkIfEmpty(prefillFormData);
        }, 100);
    }
};

export const getCustomVarMetaFrTemplate = (
    selected_custom_fields_meta,
    full_prefix,
    custom_fields_meta,
    filledCustomVars,
    formRef,
    refresh
) => {
    if (selected_custom_fields_meta?.length > 0) {
        selected_custom_fields_meta.forEach(
            (single_selected_custom_fields_meta_) => {
                let copy_field = { ...single_selected_custom_fields_meta_ };
                copy_field.key =
                    full_prefix + '_' + single_selected_custom_fields_meta_.key;
                copy_field['onChange'] = () => refresh();
                custom_fields_meta.push(copy_field);
                filledCustomVars[
                    `%${single_selected_custom_fields_meta_.key}%`
                ] = formRef?.current?.getFieldValue(copy_field.key);
            }
        );
    }
};

//handles null value for date filters
export const handleFilterClearDateIfNull = (filters) => {
    let keys = Object.keys(filters);
    for (let i = 0; i < keys.length; i++) {
        let filterKey = keys[i];
        if (filters[filterKey] == null || filters[filterKey] == undefined) {
            delete filters[filterKey];
        }
    }
    return filters;
};

export function commaSeparatedNumberForDashboard(number) {
    try {
        if (number >= 0) {
            const parts = number?.toLocaleString('en-IN').split('.');
            const formatted = parts[0].replace(/(\d)(?=(\d\d)+\d$)/g, '$1,');
            if (parts.length === 2) {
                return `${formatted}.${parts[1]}`;
            } else {
                return formatted;
            }
        }
    } catch (e) {
        return number;
    }
}

export function convertToDecimalPrefix(number) {
    const prefixes = ['', 'k', 'L', 'Cr', 'Ar', 'Mo', 'Br', 'Tr', 'Qu'];
    let exponent = 0;
    if (number >= 10000000) {
        exponent = 3; // Crore
        number = number / 10000000;
    } else if (number >= 100000) {
        exponent = 2; // Lakh
        number = number / 100000;
    } else if (number >= 1000) {
        exponent = 1; // Thousand
        number = number / 1000;
    }
    if (exponent > 0) {
        // Check if the number is a whole number
        if (Number.isInteger(number)) {
            return number + prefixes[exponent];
        } else {
            return number.toFixed(2) + prefixes[exponent];
        }
    }
    return number;
}

export const getReminders = (
    punch_in_time_fr_org,
    first_reminder_fr_org,
    factor
) => {
    punch_in_time_fr_org =
        punch_in_time_fr_org.slice(0, -2) +
        ' ' +
        punch_in_time_fr_org.slice(-2);
    function generateReminders(punchInTime, reminderTime) {
        var notifications = [];

        // Convert punchInTime to minutes
        var punchInMinutes = getMinutes(punchInTime);

        // First reminder
        var reminderMinutes = punchInMinutes - reminderTime;
        var reminder = formatTime(reminderMinutes);
        notifications.push({
            reminderTime: reminder,
            reminderMinutes: reminderTime,
        });

        // Generate subsequent notifications
        while (reminderTime > 1) {
            reminderTime = Math.ceil(reminderTime / factor);
            var notificationMinutes = punchInMinutes - reminderTime;
            var notification = formatTime(notificationMinutes);
            notifications.push({
                reminderTime: notification,
                reminderMinutes: reminderTime,
            });
        }

        function getMinutes(time) {
            var [hours, minutes, period] = time
                .match(/^(\d+):(\d+)\s+(AM|PM)$/)
                .slice(1);
            hours = parseInt(hours);
            minutes = parseInt(minutes);

            if (period === 'PM' && hours !== 12) {
                hours += 12;
            } else if (period === 'AM' && hours === 12) {
                hours = 0;
            }

            return hours * 60 + minutes;
        }

        // Helper function to format minutes to time string
        function formatTime(minutes) {
            var hours = Math.floor(minutes / 60) % 12;
            var mins = minutes % 60;
            var period = Math.floor(minutes / 60) >= 12 ? 'PM' : 'AM';
            hours = hours === 0 ? 12 : hours;

            return padZero(hours) + ':' + padZero(mins) + ' ' + period;
        }

        // Helper function to pad single digit numbers with leading zero
        function padZero(number) {
            return number.toString().padStart(2, '0');
        }
        return notifications;
    }

    function timeStringToDate(timeStr) {
        const [time, meridiem] = timeStr.split(' ');
        let [hours, minutes] = time.split(':');
        hours = parseInt(hours, 10);
        minutes = parseInt(minutes, 10);

        if (meridiem === 'PM' && hours !== 12) {
            hours += 12;
        } else if (meridiem === 'AM' && hours === 12) {
            hours = 0;
        }
        const timeInMillis = new Date(0, 0, 0, hours, minutes).getTime();
        return timeInMillis;
    }
    let reminders = generateReminders(
        punch_in_time_fr_org,
        first_reminder_fr_org
    );
    return reminders;
};

export const convertValuesOfObjToMoment = (obj) => {
    if (obj) {
        Object.keys(obj).map((key) => {
            const value = obj[key];
            if (isValidDate(value)) {
                //  debugger;
                const momentDate = moment(value, true);
                if (momentDate.isValid()) {
                    obj[key] = momentDate;
                }
            }
        });
    }

    return obj;
};
function isTimestampDateFormat(inputString) {
    return !isNaN(Date.parse(inputString));
}

function isValidDate(dateString) {
    try {
        var regEx = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateString.match(regEx)) return false; // Invalid format
        var d = new Date(dateString);
        var dNum = d.getTime();
        if (!dNum && dNum !== 0) return false; // NaN value, Invalid date
        return d.toISOString().slice(0, 10) === dateString;
    } catch (error) {
        return false;
    }
}

export const getDataForLastThreeMonths = (keys, filter) => {
    console.log('active', keys, filter);
    const endDate = moment();
    const startDate = moment().subtract(5, 'months');
    filter[keys] = [startDate, endDate].map((date) => date.toISOString());
    return filter;
};

export const getSkillLevelOptions = (isSrvcPrvdr = false) => {
    let skillLevelPrefix = isSrvcPrvdr ? 'vertical_level' : 'srvc_type_level';
    return [
        { value: `${skillLevelPrefix}_1`, label: '1(Beginner)' },
        { value: `${skillLevelPrefix}_2`, label: '2' },
        { value: `${skillLevelPrefix}_3`, label: '3' },
        { value: `${skillLevelPrefix}_4`, label: '4' },
        { value: `${skillLevelPrefix}_5`, label: '5(Expert)' },
    ];
};
export const getLabel = (key, options) => {
    let label;
    if (options?.length > 0) {
        options.forEach((singleOption) => {
            if (singleOption.value == key) {
                label = singleOption.label;
            }
        });
    }
    return label;
};

export const isTodayDate = (dateString) => {
    var givenDate = new Date(dateString);
    var currentDate = new Date();
    return givenDate.toDateString() === currentDate.toDateString();
};

export const isYesterdayDate = (dateString) => {
    var givenDate = new Date(dateString);
    var currentDate = new Date();
    var yesterday = new Date();
    yesterday.setDate(currentDate.getDate() - 1);
    return givenDate.toDateString() === yesterday.toDateString();
};

export const validateCronFrequency = (rule, value, callback) => {
    // This is a basic pattern for validating cron expressions
    // It covers typical cron patterns but might need to be adjusted for more complex cases
    const cronPattern =
        /^(\*|([0-5]?\d)) (\*|([0-5]?\d)) (\*|1?\d|2[0-3]) (\*|[1-9]|[12]\d|3[01]) (\*|[1-9]|1[0-2])$/;

    if (!value || cronPattern.test(value)) {
        callback(); // Validation passed
    } else {
        callback('Please enter a valid cron frequency (* * * * *)'); // Validation failed
    }
};

export const validateLambdaArn = (rule, value, callback) => {
    const lambdaArnPattern =
        /^arn:aws:lambda:[a-z\d-]+:\d{12}:function:[a-zA-Z0-9-_]+$/;
    if (!value || lambdaArnPattern.test(value)) {
        callback(); // Validation passed
    } else {
        callback('Please enter a valid Lambda ARN'); // Validation failed
    }
};

export const getRatingFrFilter = () => {
    let numberOfRatingValue = 5;
    let possibleRatingOptions = [];
    possibleRatingOptions = getOptionsListFrPossibleNumbers(
        numberOfRatingValue,
        (number) => ({
            color: getColorInBetweenByPercentage(
                [255, 72, 0], // Min color
                [0, 250, 156], // Max color
                number / numberOfRatingValue
            ),
        })
    ).filter((item) => item.value != 0);

    return {
        key: 'rating_value',
        label: 'Rating',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
            showSearch: true,
        },
        options: possibleRatingOptions,
    };
};
const isMobileView = () => {
    return window.innerWidth <= 768;
};
const isScreenZoomPercentage125 = () => {
    return window.devicePixelRatio == 1.25;
};

export const getMapAddressFieldsMeta = ({
    form_data,
    formRef,
    forceUpdateFn,
    showPickOnMapModel,
    togglePickOnMapModel,
    is_pincode_mandatory,
    orgSettingsData,
    prefix,
}) => {
    const filledAddress =
        getConcatenatedAddressFrmForm(prefix, formRef) ||
        getConcatenatedAddressFormData(prefix, form_data, formRef);
    const showClearFieldsButton = !!filledAddress;
    const latitude =
        formRef?.current?.getFieldValue('location_latitude') ||
        form_data?.location_latitude;
    const longitude =
        formRef?.current?.getFieldValue('location_Longitude') ||
        form_data?.location_Longitude;

    const onLatLngValueChange = async () => {
        const latitude = formRef?.current?.getFieldValue('location_latitude');
        const longitude = formRef?.current?.getFieldValue('location_Longitude');
        if (latitude && longitude) {
            const data = await getAddressBasedOnLatAndLng(latitude, longitude);
            addressFill(getAddressObj(data.results?.[0]), formRef, prefix);
        }
    };

    let clearGoogleAddressSearch = new Date().getTime();
    const clearAddress = (formRef) => {
        const keyEmptyValue = {};
        getAddressFieldKeys(prefix).forEach((singleKey) => {
            keyEmptyValue[singleKey] = '';
        });
        clearGoogleAddressSearch = new Date().getTime();
        formRef.current.setFieldsValue(keyEmptyValue);
        forceUpdateFn();
    };

    const meta = {
        formItemLayout: null,
        fields: [
            {
                key: 'mark_location_on_map',
                render: () => (
                    <div>
                        <LocationSearchInput
                            placeholder="Address"
                            useCountryAndID={true}
                            onChange={(address) => {
                                addressFill(address, formRef, prefix);
                                forceUpdateFn();
                            }}
                            orgSettingsData={orgSettingsData}
                            triggerClear={clearGoogleAddressSearch}
                        />
                        <Button onClick={togglePickOnMapModel}>
                            Pick on Map
                        </Button>
                        {showPickOnMapModel && (
                            <MapComponent
                                showPickOnMapModel={showPickOnMapModel}
                                defaultLocation={{
                                    lat: latitude || 22.5437692,
                                    lng: longitude || 79.1230844,
                                }}
                                onChange={(address) => {
                                    addressFill(address, formRef, prefix);
                                    forceUpdateFn();
                                    togglePickOnMapModel();
                                }}
                                togglePickOnMapModel={togglePickOnMapModel}
                            />
                        )}
                    </div>
                ),
            },
            {
                key: 'clear_fields',
                colSpan: 4,
                label: 'Clear fields',
                render: () =>
                    showClearFieldsButton && (
                        <Button
                            type="link"
                            onClick={() => clearAddress(formRef)}
                        >
                            Reset Address
                        </Button>
                    ),
            },
            {
                key: `${prefix}line_0`,
                colSpan: 4,
                label: 'Flat no',
                onChange: forceUpdateFn,
                rules: [{ max: 50 }],
            },
            {
                key: `${prefix}line_1`,
                colSpan: 4,
                label: 'Building/Apartment name',
                onChange: forceUpdateFn,
                rules: [{ max: 200 }],
            },
            {
                key: `${prefix}line_2`,
                label: 'Line 1',
                colSpan: 4,
                disabled: true,
                rules: [{ max: 1000 }],
            },
            {
                key: `${prefix}line_3`,
                label: 'Line 2',
                colSpan: 4,
                disabled: true,
                rules: [{ max: 200 }],
            },
            {
                key: `${prefix}pincode`,
                label: 'Pincode',
                colSpan: 2,
                required: is_pincode_mandatory,
                disabled: true,
                widgetProps: {
                    mode: 'single',
                    url: '/searcher',
                    params: { fn: 'getPincode' },
                    widgetProps: {
                        mode: 'single',
                        labelInValue: false,
                        showSearch: true,
                        style: { width: '100%' },
                    },
                },
            },
            {
                key: `${prefix}city`,
                label: 'City',
                colSpan: 2,
                disabled: true,
                widgetProps: {
                    mode: 'single',
                    url: '/searcher',
                    params: { fn: 'getCities' },
                    widgetProps: {
                        mode: 'single',
                        labelInValue: false,
                        showSearch: true,
                        style: { width: '100%' },
                    },
                },
            },
            {
                key: `${prefix}state`,
                label: 'State',
                colSpan: 4,
                disabled: true,
                widgetProps: {
                    mode: 'single',
                    url: '/searcher',
                    params: { fn: 'getState' },
                    widgetProps: {
                        mode: 'single',
                        labelInValue: false,
                        showSearch: true,
                        style: { width: '100%' },
                    },
                },
            },
            {
                key: 'location_latitude',
                label: 'Latitude',
                colSpan: 4,
                //required: !!formRef?.current?.getFieldValue('location_Longitude'),
                placeholder: 'Eg 37.7749',
                onChange: (e) => {
                    onLatLngValueChange();
                    forceUpdateFn();
                },
            },
            {
                key: 'location_Longitude',
                label: 'Longitude',
                colSpan: 4,
                // required: !!formRef?.current?.getFieldValue('location_latitude'),
                placeholder: 'Eg -122.4194',
                onChange: (e) => {
                    onLatLngValueChange();
                    forceUpdateFn();
                },
            },
            ...(latitude && longitude
                ? [
                      {
                          key: 'view_location',
                          render: () => {
                              const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
                              return (
                                  <a href={url} target="_blank">
                                      <i className="icon icon-location"> </i>
                                      View on Google Map
                                  </a>
                              );
                          },
                      },
                  ]
                : []),
        ],
    };
    return meta;
};

export {
    hasAnyFileChanged,
    getGeneralFileSection,
    getLinktoObject,
    durationAsString,
    getTouchedFieldsValueInForm,
    isTimePassed,
    getColorCodeFrStatusCategory,
    priorities,
    getTextColorFrPriority,
    convertUTCToDisplayTime,
    convertDateFieldsToMoments,
    getRandomIconByColor,
    getRandomBgColor,
    generateRandomKey,
    getAnyObjectFrFilter,
    getEmptyObjectFrFilter,
    isMobileView,
    isScreenZoomPercentage125,
};
export const setHtmlHeadTitle = (path = '/', suffixLabel = '') => {
    suffixLabel =
        typeof suffixLabel === 'string'
            ? suffixLabel
            : String(suffixLabel || '');
    path = path.replace(/\/$/, '').trim();
    const routeTitleMap = extractLinksAndLabels();
    const userDetails = ConfigHelpers.getUserDetailsInLocalStorage();
    const iconInConfig = userDetails?.org?.icon;
    const orgNickname = userDetails?.org?.nickname || '';

    // Function to find the base path by stripping dynamic segments
    const getBasePath = (currentPath) => {
        const segments = currentPath.split('/');
        // Iteratively reduce the path to find the longest matching base path in routeTitleMap
        while (segments.length > 0) {
            const potentialPath = segments.join('/') || '/';
            if (routeTitleMap[potentialPath]) {
                return potentialPath;
            }
            segments.pop();
        }
        return '/'; // Fallback to root if no match is found
    };
    const basePath = getBasePath(path);
    const basePageTitle = routeTitleMap[basePath] || '';

    let fullTitle = `${orgNickname} ${basePageTitle}`;
    // If a suffixLabel is provided, append it with a hyphen
    if (suffixLabel?.trim() !== '') {
        fullTitle += ` - ${suffixLabel}`;
    }
    fullTitle += ' TMS';

    setPageTitleAndFavIcon(fullTitle, iconInConfig);
};

export function extractLinksAndLabels() {
    const links = document.querySelectorAll('a'); // Selecting all <a> tags in the DOM

    return Array.from(links).reduce((acc, link) => {
        const href = link.getAttribute('href');

        const url = new URL(href, window.location.origin); // Ensure base URL if href is relative
        const pathname = url.pathname; // Extract only the pathname (excluding query params and hash)

        const textContent = Array.from(link.childNodes)
            .filter((node) => {
                return (
                    (node.nodeType === Node.TEXT_NODE &&
                        node.textContent.trim() !== '') ||
                    node.nodeName === 'SPAN' ||
                    node.nodeName === 'TAG'
                );
            })
            .map((node) => {
                // Use a regular expression to strip out numbers like '4.8 of 103'
                let text = node.textContent.trim();
                text = text.replace(/\d+(\.\d+)?/g, '').trim(); // Remove numeric values
                return text;
            })
            .join(' ');

        if (pathname && textContent) {
            acc[pathname] = textContent;
        }
        return acc;
    }, {});
}
export const WIFY_LOCAL_STROAGE_KEY = 'wify_data';
export const LOCAL_STORAGE_KEYS = {
    dashboard: {
        value: 'dashboard',
        my_data_widget: { value: 'my_data_widget' },
    },
    user_settings: {
        value: 'user_settings',
        show_org_details: false,
    },
    ai_chat_bot: {
        value: 'ai_chat_bot',
        isOpen: false,
    },
};

export function writeToLocalStorage({ key, value }) {
    const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);

    // Initialize empty object if nothing is stored
    if (!storedValue) {
        localStorage.setItem(WIFY_LOCAL_STROAGE_KEY, JSON.stringify({}));
    }

    let updatedValue = {};
    if (storedValue) {
        try {
            updatedValue = JSON.parse(storedValue);
        } catch (error) {
            console.log('Error parsing JSON from localStorage:', error);
        }
    }

    // Initialize the key if it doesn't exist
    if (!updatedValue[key]) {
        updatedValue[key] = value;
    } else {
        // If it's already there, we want to merge/update the value based on its type
        if (
            typeof updatedValue[key] === 'object' &&
            typeof value === 'object'
        ) {
            updatedValue[key] = { ...updatedValue[key], ...value }; // If both are objects, merge them
        } else {
            updatedValue[key] = value; // Otherwise, just replace the value
        }
    }

    // Save the updated value back to localStorage
    localStorage.setItem(WIFY_LOCAL_STROAGE_KEY, JSON.stringify(updatedValue));
}

export const findNestedKey = (obj, searchKey) => {
    if (obj === null || typeof obj !== 'object') {
        return null;
    }
    if (obj.hasOwnProperty(searchKey)) {
        return obj[searchKey];
    }
    for (const k in obj) {
        const result = findNestedKey(obj[k], searchKey);
        if (result !== null) {
            return result;
        }
    }
    return null;
};

export function readFromLocalStorage({ key }) {
    const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);

    if (storedValue) {
        try {
            const parsedValue = JSON.parse(storedValue);
            return findNestedKey(parsedValue, key);
        } catch (error) {
            console.log('Error parsing JSON from localStorage:', error);
            return null;
        }
    }

    return null; // Return null if WIFY_LOCAL_STROAGE_KEY is not found
}
export function deleteFromLocalStorage({ key }) {
    const storedValue = localStorage.getItem(WIFY_LOCAL_STROAGE_KEY);

    if (storedValue) {
        try {
            const parsedValue = JSON.parse(storedValue);

            // Helper function to delete a nested key
            const deleteNestedKey = (obj, searchKey) => {
                if (obj === null || typeof obj !== 'object') {
                    return false; // Return false if it's not an object
                }

                if (obj.hasOwnProperty(searchKey)) {
                    delete obj[searchKey];
                    return true; // Key found and deleted
                }

                for (const k in obj) {
                    if (deleteNestedKey(obj[k], searchKey)) {
                        return true; // Key found and deleted in nested object
                    }
                }
                return false; // Key not found
            };

            const isDeleted = deleteNestedKey(parsedValue, key);

            if (isDeleted) {
                localStorage.setItem(
                    WIFY_LOCAL_STROAGE_KEY,
                    JSON.stringify(parsedValue)
                );
            } else {
                console.warn(`Key "${key}" not found.`);
            }
        } catch (error) {
            console.log('Error parsing JSON from localStorage:', error);
        }
    } else {
        console.warn(`No data found in ${WIFY_LOCAL_STROAGE_KEY}.`);
    }
}

export const getFileExtension = (fileUrl) => {
    const fileName = getFileNameFrmUrl(fileUrl);
    return fileExtensionRegex.exec(fileName)[1];
};

export const getFileNameFrmUrl = (fileUrl) => {
    fileUrl = decodeURI(fileUrl.split('?')[0]);
    console.log('getFileNameFrmUrl', fileUrl);
    return fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
};

// Helper function to compare two objects based on the keys of the submitted object.
export const isEqualData = (submitted, original) => {
    return Object.keys(submitted).every((key) => {
        const submittedValue = submitted[key];
        const originalValue = original[key];

        // If the value is an array, check equality with submitted original
        if (Array.isArray(submittedValue) && Array.isArray(originalValue)) {
            return (
                JSON.stringify(submittedValue) === JSON.stringify(originalValue)
            );
        }

        return submittedValue === originalValue;
    });
};

export const getUserRightsFrService = (srvc_id, userServiceAccess) => {
    let foundServiceAccess = undefined;
    if (userServiceAccess) {
        userServiceAccess.map((singleServiceAccess) => {
            if (singleServiceAccess.menu_id == srvc_id) {
                foundServiceAccess = singleServiceAccess;
            }
        });
    }
    return foundServiceAccess?.rights_type;
};
