import React, { Component } from 'react';
import { FilePreviewerThumbnail } from 'react-file-previewer';
import Dropzone from 'react-dropzone-uploader';
import './style.css';
import Dragger from 'antd/lib/upload/Dragger';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import {
    Alert,
    Button,
    Checkbox,
    Col,
    Input,
    message,
    Popconfirm,
    Row,
    Tooltip,
} from 'antd';
import { FileIcon, defaultStyles } from 'react-file-icon';
import http_utils from '../../../util/http_utils';
import DropzoneWrapper from './DropzoneWrapper';
import ShowFilePreviewModal from './ShowFilePreviewModal';
import ImagePreviewer from './ImagePreviewer';
import { Link } from 'react-router-dom';
import DeleteFilePopconfirm from './DeleteFilePopconfirm';
import { imageFileExtensions } from '../../../util/constants';
import { getFileExtension, getFileNameFrmUrl } from '../../../util/helpers';
const re = /(?:\.([^.]+))?$/;

const dummyInitialData = [];
dummyInitialData.push('https://i.imgur.com/JOz2sFx.jpg');
dummyInitialData.push(
    'https://file-examples-com.github.io/uploads/2017/10/file-sample_150kB.pdf'
);
dummyInitialData.push(
    'https://file-examples-com.github.io/uploads/2017/04/file_example_MP4_480_1_5MG.mp4'
);
dummyInitialData.push(
    'https://file-examples-com.github.io/uploads/2017/02/file-sample_100kB.docx'
);
dummyInitialData.push(
    'https://file-examples-com.github.io/uploads/2017/02/file_example_XLSX_10.xlsx'
);

export const getModfiedUrlFrFilePreview = (props, singleFileUrl) => {
    let modifiedUrl = singleFileUrl;
    // console.log(modifiedUrl)
    if (singleFileUrl.indexOf('http') == -1) {
        modifiedUrl = props.prefixDomain + '/' + modifiedUrl;
    }
    if (props.authToken) {
        modifiedUrl =
            modifiedUrl + '?authorization=' + encodeURI(props.authToken);
    }
    if (props.extra_params) {
        modifiedUrl =
            modifiedUrl +
            '?' +
            new URLSearchParams(props.extra_params).toString();
    }
    return modifiedUrl;
};

export const getPresignedUrl = (name, extra_params) => {
    return new Promise((resolve, reject) => {
        var params = extra_params || {};
        params['file_name'] = name;
        const onComplete = (resp) => {
            resolve(resp.data);
        };
        const onError = (error) => {
            reject(http_utils.decodeErrorToMessage(error));
        };
        http_utils.performGetCall(
            '/media/signed-url',
            params,
            onComplete,
            onError,
            true
        );
    });
};
class S3Uploader extends Component {
    state = {
        showFile: '',
        uploadedFiles: [],
        initialFiles: [],
        filesModified: false,
        currentFilePreviewIndex: 0,
    };

    constructor(props) {
        super(props);
        // console.log("Inside S3 constructor",this.props.initialFiles);
        if (this.props.initialFiles) {
            // console.log("Inside S3 constructor",this.props.initialFiles);
            this.state.initialFiles = [...this.props.initialFiles];
        } else if (this.props.demoMode) {
            this.state.initialFiles = dummyInitialData;
        }
    }
    componentDidUpdate(prevProps, prevState) {
        if (prevProps.initialFiles != this.props.initialFiles) {
            this.setState({
                initialFiles: this.props.initialFiles,
            });
        }
    }

    // specify upload params and url for your files
    getUploadParams = async ({ meta: { name } }) => {
        if (this.props.customFilePrefixName) {
            name = this.props.customFilePrefixName + '_' + name;
        }
        let resp = await this.getPresignedUrl(name);
        // console.log('Rxd Resp in dropzone get upload params',resp);
        let uploadUrl = resp.url;
        let fields = resp.fields;
        let fileUrl = encodeURI(resp.fields.key);
        return { fields, meta: { fileUrl }, url: uploadUrl };
    };

    getPresignedUrl(name) {
        return new Promise((resolve, reject) => {
            var params = this.props.extra_params || {};
            debugger;
            console.log('name', name);
            params['file_name'] = name;
            console.log('params', params);
            const onComplete = (resp) => {
                resolve(resp.data);
            };
            const onError = (error) => {
                reject(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performGetCall(
                '/media/signed-url',
                params,
                onComplete,
                onError,
                true
            );
        });
    }

    // called every time a file's `status` changes
    handleChangeStatus = (fileWithMeta, status, filesWithMeta) => {
        console.log(filesWithMeta, status);
        let completedFiles = [];
        let error = false;
        filesWithMeta.map((singleFileDetails) => {
            console.log('Looping on files', singleFileDetails);
            let fileUrl = singleFileDetails.meta.fileUrl;
            let fileStatus = singleFileDetails.meta.status;
            if (fileStatus == 'done') {
                completedFiles.push(fileUrl);
            } else if (fileStatus != 'removed') {
                // console.log('Error in files',status);
                error = true;
            }
        });
        // console.log('Setting state uploaded files to',completedFiles);
        // console.log('Setting state error',error);
        this.setState(
            {
                uploadedFiles: [...completedFiles],
                uploadPending: error,
            },
            () => {
                if (!error) {
                    // tell parent ready
                    this.tellParentFilesChanged();
                    this.tellParentReadyStatusChanged(true);
                } else {
                    // tell parent not ready
                    this.tellParentReadyStatusChanged(false);
                }
            }
        );
    };

    tellParentReadyStatusChanged(status) {
        if (this.props.onReadyStatusChanged) {
            this.props.onReadyStatusChanged(status);
        }
    }

    tellParentFilesChanged(deletedFileUrl = undefined) {
        if (this.props.onFilesChanged) {
            this.props.onFilesChanged(
                this.getFinalFilesArray(),
                deletedFileUrl,
                this.state.uploadedFiles
            );
        }
    }

    // receives array of files that are done uploading when submit button is clicked
    handleSubmit = (files, allFiles) => {
        console.log(files.map((f) => f.meta));
        allFiles.forEach((f) => f.remove());
    };

    getInitialFilesFrPreview(mode) {
        let initialFiles = []; //prefixDomain
        this.state.initialFiles.map((singleFileUrl) => {
            let modifiedUrl = singleFileUrl;
            if (singleFileUrl.indexOf('http') == -1) {
                modifiedUrl = this.props.prefixDomain + '/' + modifiedUrl;
            }
            if (this.props.authToken) {
                modifiedUrl =
                    modifiedUrl +
                    '?authorization=' +
                    encodeURI(this.props.authToken);
            }
            if (this.props.extra_params) {
                modifiedUrl =
                    modifiedUrl +
                    '?' +
                    new URLSearchParams(this.props.extra_params).toString();
            }
            if (mode == 'download') {
                modifiedUrl = modifiedUrl + '?mode=' + 'download';
            }
            // if(mode == 'download'){
            //     modifiedUrl = modifiedUrl + '&response-content-disposition=attachment';
            // }
            const extension = getFileExtension(modifiedUrl)?.toLowerCase();
            const isImage = imageFileExtensions.includes(extension);
            initialFiles.push({ modifiedUrl, singleFileUrl, isImage });
        });
        console.log('INSIDE initial files modified', initialFiles);
        return initialFiles;
    }

    getInitialFilesFrDownload() {
        return this.getInitialFilesFrPreview('download');
    }
    onSingleFileClick(fileUrl) {
        // const initialFilesArray = this.getInitialFilesFrPreview();
        const allInitialFiles = this.getInitialFilesFrPreview();
        const imageFiles = allInitialFiles.filter((file) => file.isImage);
        const otherFiles = allInitialFiles.filter((file) => !file.isImage);

        let index = -1;

        index = imageFiles.findIndex((file) => file.modifiedUrl === fileUrl);

        if (index == -1) {
            index = otherFiles.findIndex(
                (file) => file.modifiedUrl === fileUrl
            );
        }

        if (index == -1) {
            console.log('No File Found');
        }

        this.setState({
            showFile: fileUrl,
            currentFilePreviewIndex: index,
        });
    }

    shouldUseFileViewer(fileUrl) {
        let fileViewerGoodFr = [
            'mp4',
            'webm',
            'mp3',
            'csv',
            'docx',
            'doc',
            'pdf',
            'xls',
            'xlsx',
        ];
        let currFileExtensio = getFileExtension(fileUrl);
        return fileViewerGoodFr.includes(currFileExtensio);
    }

    shouldUseFilePreviewerThumb(fileUrl) {
        let filePreviewerGoodFr = ['png', 'jpeg', 'JPEG', 'JPG', 'jpg', 'PNG'];
        let currFileExtensio = getFileExtension(fileUrl);
        return filePreviewerGoodFr.includes(currFileExtensio);
    }

    onDeleteFileClick = (fileUrl) => {
        let currentFinalArray = [...this.state.initialFiles];
        let fileRemoved = false;

        // Loop through the initialFiles array to find and remove the file
        for (let index = 0; index < currentFinalArray.length; index++) {
            if (currentFinalArray[index] === fileUrl) {
                currentFinalArray.splice(index, 1);
                fileRemoved = true;
                break; // Exit loop after removing the file
            }
        }

        if (!fileRemoved) {
            return message.error('File not found.');
        }
        this.setState(
            {
                initialFiles: currentFinalArray,
                filesModified: true,
            },
            () => {
                message.success(
                    `${getFileNameFrmUrl(fileUrl)} deleted successfully`
                );
                this.tellParentFilesChanged(fileUrl);
            }
        );
    };

    getFinalFilesArray() {
        return [...this.state.initialFiles, ...this.state.uploadedFiles];
    }
    getSelectedFileUrl(singleFileUrl) {
        // console.log('getSelectedFileUrl',this.props.sectionKey)
        if (this.props.onFileSelected) {
            this.props.onFileSelected(singleFileUrl);
        }
    }
    getOtherAttachments(otherFiles, downloadOtherFilesUrls) {
        return otherFiles.map(({ modifiedUrl, singleFileUrl }, index) => (
            <Col
                xs={12}
                sm={12}
                md={6}
                lg={this.props.maxColSpan ? this.props.maxColSpan : 3}
                key={index}
                className="gx-mt-2"
            >
                <div
                    className={
                        this.props.customPreviewCss
                            ? 'gx-user-fnd wy-image-adjust-thumb other-documents-container wy-cursor-pointer'
                            : 'gx-user-fnd other-documents-container wy-cursor-pointer'
                    }
                    style={{
                        height: this.props.customPreviewHeight || '150px',
                    }}
                    onClick={(e) => this.onSingleFileClick(modifiedUrl)}
                >
                    {this.shouldUseFilePreviewerThumb(modifiedUrl) ? (
                        <FilePreviewerThumbnail
                            file={{ url: modifiedUrl }}
                            hideControls
                        />
                    ) : (
                        <div className="gx-d-flex gx-justify-content-center gx-align-items-center ">
                            <div
                                style={{
                                    maxWidth:
                                        this.props.customFileIconMaxWidth ||
                                        '50%',
                                    padding:
                                        this.props.customFileIconPadding || '0',
                                }}
                                className="gx-mt-1"
                            >
                                <FileIcon
                                    extension={
                                        '.' + getFileExtension(modifiedUrl)
                                    }
                                    {...defaultStyles[
                                        getFileExtension(modifiedUrl)
                                    ]}
                                />
                            </div>

                            {/* <Avatar
                                        size={75}
                                        // gap={}
                                        // className={'gx-bg-red'}
                                    >{`.${re.exec(modifiedUrl)[1]}`}</Avatar> */}
                        </div>
                    )}

                    <div
                        className="other-documents-footer"
                        onClick={(e) => this.onSingleFileClick(modifiedUrl)}
                    >
                        {' '}
                        <Tooltip
                            placement="top"
                            title={getFileNameFrmUrl(modifiedUrl)}
                        >
                            <p className="wy-attachment-name">
                                {getFileNameFrmUrl(singleFileUrl)}
                            </p>
                        </Tooltip>
                        <div className="other-documents-footer-buttons">
                            <Link
                                to={{
                                    pathname:
                                        downloadOtherFilesUrls[index]
                                            ?.modifiedUrl,
                                }}
                                download={getFileNameFrmUrl(
                                    downloadOtherFilesUrls[index]?.modifiedUrl
                                )}
                                className="gx-d-flex"
                                target="_blank"
                                onClick={(e) => e.stopPropagation()}
                            >
                                <Button
                                    size="small"
                                    type="link"
                                    icon={<DownloadOutlined />}
                                />
                            </Link>
                            <div className="gx-d-flex">
                                {
                                    <DeleteFilePopconfirm
                                        getFileNameFrmUrl={getFileNameFrmUrl}
                                        modifiedUrl={modifiedUrl}
                                        singleFileUrl={singleFileUrl}
                                        onDelete={this.onDeleteFileClick}
                                        showDeleteBtn={this.props.showDeleteBtn}
                                    />
                                }
                            </div>
                        </div>
                        {this.props.selectOnly && (
                            <Checkbox
                                onClick={(e) => {
                                    e.stopPropagation();
                                    // console.log('checkbox',e.target.checked)
                                    this.getSelectedFileUrl(singleFileUrl);
                                }}
                            ></Checkbox>
                        )}
                    </div>
                </div>
            </Col>
        ));
    }
    render() {
        const {
            showFile,
            filesModified,
            initialFiles,
            currentFilePreviewIndex,
        } = this.state;
        // console.log("PrefixDomain",this.props.prefixDomain);
        const totalFiles = initialFiles ? initialFiles.length : 0;

        const allInitialFiles = this.getInitialFilesFrPreview();
        const allInitialFilesDownload = this.getInitialFilesFrDownload();

        const imageFiles = allInitialFiles.filter((file) => file.isImage);
        const otherFiles = allInitialFiles.filter((file) => !file.isImage);
        const downloadOtherFiles = allInitialFilesDownload.filter(
            (file) => !file.isImage
        );
        console.log('downloadOtherFiles', downloadOtherFiles);
        return (
            <div
            // {...this.props}
            >
                {/* pathToGetSignedUrl={'/third-party/link-generator-s3'} */}
                {this.props.demoMode && (
                    <div>
                        <Alert
                            message="Running in demo mode, output will be shown below"
                            type="info"
                        />
                        <Input.TextArea
                            className="gx-bg-dark gx-text-white"
                            autoSize={{ minRows: 2, maxRows: 30 }}
                            value={JSON.stringify(this.getFinalFilesArray())}
                            // onChange={this.onJsonChange}
                        />
                        <hr></hr>
                    </div>
                )}
                {filesModified && (
                    <Alert
                        message="**Unsaved changes, submit form to apply changes"
                        type="warning"
                    />
                )}
                {!this.props.miniThumbnailView && imageFiles.length > 0 && (
                    <div
                        className={`image-files-section ${this.props?.compConfig?.name}-s3`}
                    >
                        <h3>Images</h3>
                        <ImagePreviewer
                            imageFiles={imageFiles}
                            getFileNameFrmUrl={getFileNameFrmUrl}
                            onDelete={this.onDeleteFileClick}
                            showDeleteBtn={this.props.showDeleteBtn}
                            parentCompConfig={this.props?.compConfig}
                            selectOnly={this?.props?.selectOnly || null}
                            onFileSelected={this?.props?.onFileSelected || null}
                        />
                    </div>
                )}

                {!this.props.miniThumbnailView && otherFiles.length > 0 && (
                    <div
                        className={`rest-files-section ${this.props?.compConfig?.name}-s3`}
                    >
                        <h3>Other Attachments</h3>
                        <Row gutter="10" className="gx-mb-3">
                            {this.getOtherAttachments(
                                otherFiles,
                                downloadOtherFiles
                            )}
                        </Row>
                    </div>
                )}
                {this.props.miniThumbnailView && (
                    <div
                        className={`image-files-section ${this.props?.compConfig?.name}-s3-mini-thumbview`}
                    >
                        {imageFiles.length > 0 && (
                            <ImagePreviewer
                                imageFiles={imageFiles}
                                getFileNameFrmUrl={getFileNameFrmUrl}
                                onDelete={this.onDeleteFileClick}
                                miniThumbnailView={this.props.miniThumbnailView}
                                showDeleteBtn={this.props.showDeleteBtn}
                                parentCompConfig={this.props.compConfig}
                                selectOnly={this?.props?.selectOnly || null}
                                onFileSelected={
                                    this?.props?.onFileSelected || null
                                }
                            />
                        )}
                        {otherFiles.length > 0 &&
                            this.getOtherAttachments(
                                otherFiles,
                                downloadOtherFiles
                            )}
                    </div>
                )}

                {!this.props.readOnly && (
                    <DropzoneWrapper
                        required={this.props.required}
                        getUploadParams={this.getUploadParams}
                        files={this.state.uploadedFiles}
                        onChangeStatus={this.handleChangeStatus}
                        totalFiles={totalFiles}
                        disabled={this.props.disabled || false}
                        // onSubmit={this.handleSubmit}
                        inputContent={
                            <p className="gx-text-center gx-mb-0 h4 gx-text-primary">
                                <UploadOutlined /> Drag or click to upload
                            </p>
                        }
                        // accept="image/*,audio/*,video/*"
                    />
                )}

                {showFile != '' && (
                    <ShowFilePreviewModal
                        fileNameFrmUrl={getFileNameFrmUrl(showFile)}
                        closeModal={() => this.setState({ showFile: '' })}
                        getFileExtension={getFileExtension(showFile)}
                        totalFiles={otherFiles || []}
                        updateShowFile={this.updateShowFile}
                        onError={this.onError}
                        index={currentFilePreviewIndex}
                    />
                )}

                {/* <Dragger 
                    name = 'request_files'
                    className="gx-w-100 gx-d-block"
                    style={{
                        height: '100px !important'
                    }}
                    multiple = {true}>
                    <p className="ant-upload-drag-icon">
                        <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">Click or drag file to this area to upload</p>
                    <p className="ant-upload-hint">
                        Support for a single or multiple files. 
                    </p>
                </Dragger> */}
            </div>
        );
    }
}

S3Uploader.propTypes = {};

export default S3Uploader;
