const { createBullBoard } = require('@bull-board/api');
const { BullAdapter } = require('@bull-board/api/bullAdapter');
const { ExpressAdapter } = require('@bull-board/express');
const { allQueues } = require('./queues');

// Create the Express adapter for Bull Board
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

// Function to categorize queues for better organization
const categorizeQueues = () => {
    const categories = {
        workflow: [],
        communication: [],
        cron: [],
        processing: [],
        other: [],
    };

    Object.keys(allQueues).forEach((queueName) => {
        if (queueName.includes('WORKFLOW')) {
            categories.workflow.push(queueName);
        } else if (
            queueName.includes('SMS') ||
            queueName.includes('WHATSAPP') ||
            queueName.includes('EMAIL') ||
            queueName.includes('NOTIFICATION')
        ) {
            categories.communication.push(queueName);
        } else if (queueName.startsWith('CRON_')) {
            categories.cron.push(queueName);
        } else if (
            queueName.includes('PROCESS') ||
            queueName.includes('BULK') ||
            queueName.includes('EXPORT')
        ) {
            categories.processing.push(queueName);
        } else {
            categories.other.push(queueName);
        }
    });

    return categories;
};

// Function to initialize Bull Dashboard
const initializeBullDashboard = (app) => {
    // Create queue instances for the dashboard
    const queueAdapters = [];
    const categories = categorizeQueues();

    console.log('📊 Initializing Bull Dashboard with queue categories:');
    Object.keys(categories).forEach((category) => {
        if (categories[category].length > 0) {
            console.log(
                `  ${category.toUpperCase()}: ${categories[category].length} queues`
            );
        }
    });

    // Add all queues to the dashboard in organized manner
    Object.keys(allQueues).forEach((queueIdentifier) => {
        try {
            // Get the existing queue instance from the app
            let queue = app.get(`Queue_${queueIdentifier}`);

            if (queue) {
                // Create adapter with custom name for better identification
                const adapter = new BullAdapter(queue, {
                    readOnlyMode: false, // Allow job management from dashboard
                    allowRetries: true, // Allow retrying failed jobs
                    description: `Processor: ${allQueues[queueIdentifier].processor || 'N/A'}`,
                });

                queueAdapters.push(adapter);
                console.log(`  ✅ Added queue: ${queueIdentifier}`);
            } else {
                console.log(`  ❌ Queue not found in app: ${queueIdentifier}`);
            }
        } catch (error) {
            console.log(
                `  ⚠️  Error accessing queue ${queueIdentifier}:`,
                error.message
            );
        }
    });

    // Create Bull Board with all queue adapters
    createBullBoard({
        queues: queueAdapters,
        serverAdapter: serverAdapter,
        options: {
            uiConfig: {
                boardTitle: 'WIFY TMS Queue Dashboard',
                boardLogo: {
                    path: '',
                    width: 120,
                    height: 40,
                },
                miscLinks: [{ text: 'Application', url: '/' }],
                favIcon: {
                    default: 'static/images/logo.svg',
                    alternative: 'static/favicon.ico',
                },
            },
        },
    });

    // Mount the dashboard on the Express app
    app.use('/admin/queues', serverAdapter.getRouter());

    console.log(`🎯 Bull Dashboard initialized successfully!`);
    console.log(
        `📍 Access dashboard at: http://localhost:${process.env.PORT || 3000}/admin/queues`
    );
    console.log(`📈 Monitoring ${queueAdapters.length} queues from queues.js`);

    return serverAdapter;
};

// Function to get queue statistics for monitoring
const getQueueStats = async (app) => {
    const stats = {};

    for (const queueIdentifier of Object.keys(allQueues)) {
        try {
            const queue = app.get(`Queue_${queueIdentifier}`);
            if (queue) {
                const waiting = await queue.getWaiting();
                const active = await queue.getActive();
                const completed = await queue.getCompleted();
                const failed = await queue.getFailed();
                const delayed = await queue.getDelayed();

                stats[queueIdentifier] = {
                    waiting: waiting.length,
                    active: active.length,
                    completed: completed.length,
                    failed: failed.length,
                    delayed: delayed.length,
                    processor: allQueues[queueIdentifier].processor,
                    total:
                        waiting.length +
                        active.length +
                        completed.length +
                        failed.length +
                        delayed.length,
                };
            }
        } catch (error) {
            stats[queueIdentifier] = {
                error: error.message,
                processor: allQueues[queueIdentifier].processor,
            };
        }
    }

    return stats;
};

// Function to clean old completed/failed jobs across all queues
const cleanAllQueues = async (app, options = {}) => {
    const defaultOptions = {
        grace: 1000 * 60 * 60, // 1 hour
        limit: 100,
        ...options,
    };

    const results = {};

    for (const queueIdentifier of Object.keys(allQueues)) {
        try {
            const queue = app.get(`Queue_${queueIdentifier}`);
            if (queue) {
                const completedCleaned = await queue.clean(
                    defaultOptions.grace,
                    'completed',
                    defaultOptions.limit
                );
                const failedCleaned = await queue.clean(
                    defaultOptions.grace,
                    'failed',
                    defaultOptions.limit
                );

                results[queueIdentifier] = {
                    completedCleaned: completedCleaned.length,
                    failedCleaned: failedCleaned.length,
                };
            }
        } catch (error) {
            results[queueIdentifier] = {
                error: error.message,
            };
        }
    }

    return results;
};

module.exports = {
    initializeBullDashboard,
    serverAdapter,
    getQueueStats,
    cleanAllQueues,
    categorizeQueues,
};
