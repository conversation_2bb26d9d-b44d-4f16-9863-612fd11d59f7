const { createBullBoard } = require('@bull-board/api');
const { BullAdapter } = require('@bull-board/api/bullAdapter');
const { ExpressAdapter } = require('@bull-board/express');
const { allQueues } = require('./queues');

// Create the Express adapter for Bull Board
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

// Function to categorize queues for better organization
const categorizeQueues = () => {
    const categories = {
        workflow: [],
        communication: [],
        cron: [],
        processing: [],
        other: [],
    };

    Object.keys(allQueues).forEach((queueName) => {
        if (queueName.includes('WORKFLOW')) {
            categories.workflow.push(queueName);
        } else if (
            queueName.includes('SMS') ||
            queueName.includes('WHATSAPP') ||
            queueName.includes('EMAIL') ||
            queueName.includes('NOTIFICATION')
        ) {
            categories.communication.push(queueName);
        } else if (queueName.startsWith('CRON_')) {
            categories.cron.push(queueName);
        } else if (
            queueName.includes('PROCESS') ||
            queueName.includes('BULK') ||
            queueName.includes('EXPORT')
        ) {
            categories.processing.push(queueName);
        } else {
            categories.other.push(queueName);
        }
    });

    return categories;
};

// Optional basic authentication middleware for Bull Dashboard
const basicAuthMiddleware = (req, res, next) => {
    // Skip auth if DISABLE_BULL_DASHBOARD_AUTH is set
    if (process.env.DISABLE_BULL_DASHBOARD_AUTH === 'true') {
        return next();
    }

    const auth = req.headers.authorization;
    if (!auth || !auth.startsWith('Basic ')) {
        res.setHeader('WWW-Authenticate', 'Basic realm="Bull Dashboard"');
        return res.status(401).send('Authentication required');
    }

    const credentials = Buffer.from(auth.slice(6), 'base64')
        .toString()
        .split(':');
    const username = credentials[0];
    const password = credentials[1];

    // Default credentials (change these in production!)
    const validUsername = process.env.BULL_DASHBOARD_USERNAME || 'admin';
    const validPassword = process.env.BULL_DASHBOARD_PASSWORD || 'admin123';

    if (username === validUsername && password === validPassword) {
        next();
    } else {
        res.setHeader('WWW-Authenticate', 'Basic realm="Bull Dashboard"');
        res.status(401).send('Invalid credentials');
    }
};

// Function to initialize Bull Dashboard
const initializeBullDashboard = (app) => {
    // Create queue instances for the dashboard
    const queueAdapters = [];
    const categories = categorizeQueues();

    console.log('📊 Initializing Bull Dashboard with queue categories:');
    Object.keys(categories).forEach((category) => {
        if (categories[category].length > 0) {
            console.log(
                `  ${category.toUpperCase()}: ${categories[category].length} queues`
            );
        }
    });

    // Add all queues to the dashboard in organized manner
    Object.keys(allQueues).forEach((queueIdentifier) => {
        try {
            // Get the existing queue instance from the app
            let queue = app.get(`Queue_${queueIdentifier}`);

            if (queue) {
                // Create adapter with custom name for better identification
                const adapter = new BullAdapter(queue, {
                    readOnlyMode: false, // Allow job management from dashboard
                    allowRetries: true, // Allow retrying failed jobs
                    description: `Processor: ${allQueues[queueIdentifier].processor || 'N/A'}`,
                    // Enable real-time updates
                    enableMetrics: true,
                });

                queueAdapters.push(adapter);
                console.log(`  ✅ Added queue: ${queueIdentifier}`);
            } else {
                console.log(`  ❌ Queue not found in app: ${queueIdentifier}`);
            }
        } catch (error) {
            console.log(
                `  ⚠️  Error accessing queue ${queueIdentifier}:`,
                error.message
            );
        }
    });

    // Create Bull Board with all queue adapters
    createBullBoard({
        queues: queueAdapters,
        serverAdapter: serverAdapter,
        options: {
            uiConfig: {
                boardTitle: 'WIFY TMS Queue Dashboard',
                boardLogo: {
                    path: '',
                    width: 120,
                    height: 40,
                },
                miscLinks: [{ text: 'Application', url: '/' }],
                favIcon: {
                    default: 'static/images/logo.svg',
                    alternative: 'static/favicon.ico',
                },
                // Enable auto-refresh
                pollingTimeMs: 2000, // Refresh every 2 seconds
            },
        },
    });

    // Add test route directly to app
    app.get('/admin/queues/test', (req, res) => {
        res.json({
            message: 'Bull Dashboard route is working!',
            timestamp: new Date().toISOString(),
            queues: Object.keys(allQueues).length,
        });
    });

    // Add route to trigger test jobs for monitoring
    app.post('/admin/queues/trigger-test-job/:queueName', (req, res) => {
        const { queueName } = req.params;

        if (!allQueues[queueName]) {
            return res.status(404).json({
                error: `Queue ${queueName} not found`,
                availableQueues: Object.keys(allQueues),
            });
        }

        try {
            // Add a test job to the specified queue
            const testJobData = {
                test: true,
                message: `Test job triggered from Bull Dashboard at ${new Date().toISOString()}`,
                queueName: queueName,
                triggeredBy: 'dashboard',
            };

            allQueues[queueName].addJob(testJobData, {
                delay: 2000, // 2 second delay to see it in dashboard
            });

            res.json({
                success: true,
                message: `Test job added to ${queueName}`,
                jobData: testJobData,
            });
        } catch (error) {
            res.status(500).json({
                error: 'Failed to add test job',
                details: error.message,
            });
        }
    });

    // Simple approach: Mount the dashboard with conditional authentication
    if (process.env.DISABLE_BULL_DASHBOARD_AUTH === 'true') {
        console.log('Bull Dashboard auth disabled');
        // No authentication - directly mount the bull board router
        app.use(
            '/admin/queues',
            (req, res, next) => {
                // Skip any existing auth middleware for this route
                req.skipAuth = true;
                next();
            },
            serverAdapter.getRouter()
        );
        console.log(
            '⚠️  Bull Dashboard mounted WITHOUT authentication (development mode)'
        );
    } else {
        // With basic authentication
        app.use(
            '/admin/queues',
            basicAuthMiddleware,
            serverAdapter.getRouter()
        );
        console.log('🔒 Bull Dashboard mounted WITH basic authentication');
        console.log(
            `   Username: ${process.env.BULL_DASHBOARD_USERNAME || 'admin'}`
        );
        console.log(
            `   Password: ${process.env.BULL_DASHBOARD_PASSWORD || 'admin123'}`
        );
    }

    console.log(`🎯 Bull Dashboard initialized successfully!`);
    console.log(
        `📍 Access dashboard at: http://localhost:${process.env.PORT || 3000}/admin/queues`
    );
    console.log(`📈 Monitoring ${queueAdapters.length} queues from queues.js`);
    console.log(`🔧 Dashboard mounted at path: /admin/queues`);
    console.log(
        `🔧 Auth disabled: ${process.env.DISABLE_BULL_DASHBOARD_AUTH === 'true'}`
    );

    return serverAdapter;
};

// Function to get queue statistics for monitoring
const getQueueStats = async (app) => {
    const stats = {};

    for (const queueIdentifier of Object.keys(allQueues)) {
        try {
            const queue = app.get(`Queue_${queueIdentifier}`);
            if (queue) {
                const waiting = await queue.getWaiting();
                const active = await queue.getActive();
                const completed = await queue.getCompleted();
                const failed = await queue.getFailed();
                const delayed = await queue.getDelayed();

                stats[queueIdentifier] = {
                    waiting: waiting.length,
                    active: active.length,
                    completed: completed.length,
                    failed: failed.length,
                    delayed: delayed.length,
                    processor: allQueues[queueIdentifier].processor,
                    total:
                        waiting.length +
                        active.length +
                        completed.length +
                        failed.length +
                        delayed.length,
                };
            }
        } catch (error) {
            stats[queueIdentifier] = {
                error: error.message,
                processor: allQueues[queueIdentifier].processor,
            };
        }
    }

    return stats;
};

// Function to clean old completed/failed jobs across all queues
const cleanAllQueues = async (app, options = {}) => {
    const defaultOptions = {
        grace: 1000 * 60 * 60, // 1 hour
        limit: 100,
        ...options,
    };

    const results = {};

    for (const queueIdentifier of Object.keys(allQueues)) {
        try {
            const queue = app.get(`Queue_${queueIdentifier}`);
            if (queue) {
                const completedCleaned = await queue.clean(
                    defaultOptions.grace,
                    'completed',
                    defaultOptions.limit
                );
                const failedCleaned = await queue.clean(
                    defaultOptions.grace,
                    'failed',
                    defaultOptions.limit
                );

                results[queueIdentifier] = {
                    completedCleaned: completedCleaned.length,
                    failedCleaned: failedCleaned.length,
                };
            }
        } catch (error) {
            results[queueIdentifier] = {
                error: error.message,
            };
        }
    }

    return results;
};

module.exports = {
    initializeBullDashboard,
    serverAdapter,
    getQueueStats,
    cleanAllQueues,
    categorizeQueues,
};
