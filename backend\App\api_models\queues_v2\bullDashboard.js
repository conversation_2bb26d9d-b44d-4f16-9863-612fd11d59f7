const { createBullBoard } = require('@bull-board/api');
const { BullAdapter } = require('@bull-board/api/bullAdapter');
const { ExpressAdapter } = require('@bull-board/express');
const Queue = require('bull');
const { allQueues } = require('./queues');

// Create the Express adapter for Bull Board
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

// Function to initialize Bull Dashboard
const initializeBullDashboard = (app) => {
    const bull_redis = process.env.BULL_REDIS_URL || 'redis://localhost:6379';
    
    // Create queue instances for the dashboard
    const queueAdapters = [];
    
    // Add all queues to the dashboard
    Object.keys(allQueues).forEach((queueIdentifier) => {
        // Get the existing queue instance from the app or create a new one for dashboard
        let queue;
        try {
            queue = app.get(`Queue_${queueIdentifier}`);
        } catch (error) {
            // If queue doesn't exist in app, create a new instance for dashboard
            queue = new Queue(queueIdentifier, bull_redis);
        }
        
        if (queue) {
            queueAdapters.push(new BullAdapter(queue));
        }
    });
    
    // Create Bull Board with all queue adapters
    createBullBoard({
        queues: queueAdapters,
        serverAdapter: serverAdapter,
    });
    
    // Mount the dashboard on the Express app
    app.use('/admin/queues', serverAdapter.getRouter());
    
    console.log('Bull Dashboard initialized at /admin/queues 📊');
    
    return serverAdapter;
};

module.exports = {
    initializeBullDashboard,
    serverAdapter
};
