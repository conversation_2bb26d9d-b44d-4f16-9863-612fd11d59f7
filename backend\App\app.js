const envFileSuffix = process.env.NODE_ENV ? '.' + process.env.NODE_ENV : '';
require('dotenv').config(
    // { path: './.env' + (process.env.NODE_ENV == "production") ? ".production" : '' }
    { path: `./.env${envFileSuffix}` }
);
const os = require('os');
const crypto = require('crypto');
var createError = require('http-errors');
var express = require('express');
var path = require('path');
var logger = require('morgan');
var massive = require('massive');
const { Client: pgClient } = require('pg');
const { migrate } = require('postgres-migrations'); // For DB migrations
var cors = require('cors');
const bodyParser = require('body-parser');
const Queue = require('bull');
var machineHash = crypto
    .createHash('md5')
    .update(os.hostname())
    .digest('binary'); // Math.random() ;
var app = express();
var MACHINE_ID = 'TMS_APP_SERVER_' + os.hostname();
app.set('MACHINE_ID', MACHINE_ID);
var connectionString =
    process.env.DB_URL +
    '?' +
    new URLSearchParams({
        application_name: 'TMS_APP_SERVER_' + os.hostname(),
    }).toString();
var connectionStringReplica =
    process.env.DB_URL_REPLICA +
    '?' +
    new URLSearchParams({
        application_name: 'TMS_APP_SERVER_' + os.hostname(),
    }).toString();
var connectionStringDump =
    process.env.DB_URL_DUMP +
    '?' +
    new URLSearchParams({
        application_name: 'TMS_APP_SERVER_' + os.hostname(),
    }).toString();
//conditionally load only if the environment is production
if (process.env.NODE_ENV == 'production') {
    console.log(
        'The application is now running in the production environment, and New Relic is being loaded.'
    );
    require('newrelic');
}
const helmet = require('helmet');

let domain = process.env.DOMAIN;
app.use(
    helmet.contentSecurityPolicy({
        directives: {
            'script-src': ["'self'", domain],
            'font-src': ["'self'", domain],
            frameAncestors: [domain], //This allows the web application to be embedded within iframes on the same origin and on the specifie
        },
    })
);

//--------------------< Route Map >----------------------//
// Register your modules here

const routeMap = require('./routes/map');

const { allQueues } = require('./api_models/queues_v2/queues');
const { generateHash } = require('./api_models/utils/crypto');
const {
    initializeBullDashboard,
} = require('./api_models/queues_v2/bullDashboard');
const appDir = path.dirname(require.main.path);
const bull_redis = process.env.BULL_REDIS_URL || 'redis://localhost:6379';

//--------------------< Essentials >----------------------//
//--------------------------------------------------------//
setupEssentials = () => {
    // view engine setup
    app.use(cors());
    app.use(bodyParser.json({ limit: process.env.POST_BODY_SIZE_LIMIT }));
    app.use(
        bodyParser.urlencoded({
            limit: process.env.POST_BODY_SIZE_LIMIT,
            extended: true,
            parameterLimit: 50000,
        })
    );
    app.set('trust proxy', true);
    app.set('views', path.join(__dirname, 'views'));
    app.set('view engine', 'jade');
    app.use(logger('dev'));
    app.use(express.json());
    app.use(express.urlencoded({ extended: false }));
    //NOTE: we deal with JWT, hence cookie not needed
    // app.use(cookieParser());
    app.use(express.static(path.join(__dirname, 'public')));
    console.log('Essentials init done 👍');

    connectToFirebaseAdmin();
};

//--------------------< Routes >----------------------//
//--------------------------------------------------------//
setupRoutes = () => {
    for (let i = 0; i < routeMap.length; i++) {
        const R = routeMap[i];
        // console.log(`ROUTEEEEE>>>>>`,R);
        console.log(`Initializing ${R.route_file} --> ${R.path}`);
        const router = require(R.route_file);
        if (R.middleware && R.middleware.length > 0) {
            app.use(R.path, ...[...R.middleware, router]);
        }
        app.use(R.path, router);
        console.log(`👍`);
    }
};

//------------------< Error Handlers >--------------------//
//--------------------------------------------------------//
setupErrorHandlers = () => {
    // catch 404 and forward to error handler
    app.use(function (req, res, next) {
        next(createError(404));
    });

    // error handler
    app.use(function (err, req, res, next) {
        // set locals, only providing error in development
        res.locals.message = err.message;
        res.locals.error = req.app.get('env') === 'development' ? err : {};
        // render the error page
        res.status(err.status || 500);
        res.render('error');
    });

    console.log('Error handlers setup done 👍');
};

//------------------< Error Handlers >--------------------//
//--------------------------------------------------------//

updateDBFunctions = async (client) => {
    if (process.env.DISABLE_DB_FUNCTIONS_SYNC == 1) {
        console.log('DB function sync disabled');
        return true;
    }
    const DBFunctionsFolder = './dbFunctions/';
    const fs = require('fs');
    let dbFnFiles;
    try {
        dbFnFiles = fs.readdirSync(DBFunctionsFolder);
    } catch (error) {
        console.log('DB function directory not found');
        return true;
    }
    let fullSqlOfFns = '';
    let hashes = '';
    for (let i = 0; i < dbFnFiles.length; i++) {
        const dbFnFile = dbFnFiles[i];
        // console.log(dbFn);
        console.log('Reading db fn', dbFnFile);
        const dbFn = fs.readFileSync(DBFunctionsFolder + dbFnFile).toString();
        let fileHash = generateHash(dbFn);
        hashes += fileHash;
        fullSqlOfFns += ';' + dbFn;
    }
    if (fullSqlOfFns != '') {
        // check if this version of files has
        // already run
        try {
            const existingRuns = await client.query(
                `select * from function_migrations where hashes='${hashes}'`
            );
            if (existingRuns.rows.length > 0) {
                return true;
            }
        } catch (error) {
            console.log(error);
            return;
        }
        fullSqlOfFns +=
            ';' +
            `INSERT INTO public.function_migrations (hashes)
      VALUES ('${hashes}');`;

        // we have some functions!
        const shouldAbort = (err) => {
            if (err) {
                console.log('Error Updating functions -', err.stack);
                client.query('ROLLBACK', (err) => {
                    if (err) {
                        console.log(
                            'Error rolling back functions update',
                            err.stack
                        );
                    }
                });
            }
            return !!err;
        };
        const Confirm = require('prompt-confirm');
        const prompt = new Confirm({
            name: 'sync-db-functions',
            message:
                'Sync DB functions\n(Your DB functions will be overwritten) ?',
        });
        let answer =
            process.env.ASK_SYNC_DB_FUNCTIONS == 1 ? await prompt.run() : true;
        if (answer) {
            try {
                const beginResp = await client.query('BEGIN');
            } catch (error) {
                if (shouldAbort(error)) return;
            }
            try {
                const fnsUpdateRes = await client.query(fullSqlOfFns);
                const commitRes = await client.query('COMMIT');
                console.log('Functions update successfull!!');
                return true;
            } catch (error) {
                if (shouldAbort(error)) return;
            }
        } else {
            console.log('DB functions not synced');
            return true;
        }
    } else {
        return true;
    }
};
// setup migrations
setupMigrations = (db) => {
    const client = new pgClient(connectionString);
    let migrationsDirectory = path.join(__dirname, 'migrations');
    client
        .connect()
        .then(() => {
            // pg must be connected now
            // console.log('PG connected',client);
            migrate({ client }, migrationsDirectory)
                .then(async (migrations) => {
                    console.info('completed migrations - ', migrations);
                    let updateFnRes = await updateDBFunctions(client);
                    if (updateFnRes) {
                        connectToMassiveAndStart();
                        setTimeout(() => {
                            connectToDBReplicaAndStart();
                            connectToDBDumpAndStart();
                        }, 1000);
                    } else {
                        console.error(
                            'Fix DB functions..Cannot proceed ahead..'
                        );
                    }
                })
                .catch((err) => console.error(err));
        })
        .catch((e) => {
            console.log(e);
        });
};

setupMigrations();

setupQueues = () => {
    console.log('Setting up queues');
    const proccessorsDirPath = appDir + '/api_models/queues_v2/processors';
    Object.keys(allQueues).forEach((queueIdentifier) => {
        const singleQueueDetails = allQueues[queueIdentifier];
        const newQueue = new Queue(queueIdentifier, bull_redis);
        if (singleQueueDetails.ons) {
            Object.keys(singleQueueDetails.ons).forEach((on) => {
                newQueue.on(on, singleQueueDetails.ons[on]);
            });
        }
        // For a single background job processor instance
        if (process.env.NOT_A_JOB_PROCESSOR != 1) {
            const processorFunction = require(
                proccessorsDirPath + singleQueueDetails.processor
            ).default;
            newQueue.process((job, done) => {
                processorFunction(job, done);
            });
        }

        app.set(`Queue_${queueIdentifier}`, newQueue);
        const addJobFunction = (job, options) => {
            options = {
                ...options,
                attempts: 2,
                // Keep some jobs for monitoring in Bull Dashboard
                removeOnComplete:
                    process.env.NODE_ENV === 'production' ? 10 : 50, // Keep last 10-50 completed jobs
                removeOnFail: process.env.NODE_ENV === 'production' ? 10 : 50, // Keep last 10-50 failed jobs
            };
            app.get(`Queue_${queueIdentifier}`).add(job, options);
        };
        app.set(`Queue_${queueIdentifier}_ADD`, addJobFunction);
        console.log(`Initialised queue - ${queueIdentifier} 👍`);
    });
    console.log('Setting up queues - Complete');
};

// Essential: we ensure that the DB/storage connection is established
//----------------< App Initialization >------------------//
//--------------------------------------------------------//

connectToMassiveAndStart = () => {
    massive(connectionString)
        .then((massiveInstance) => {
            console.log('Database connection is established 👍');
            app.set(`db`, massiveInstance);
            app.set('start_time', new Date());

            setupEssentials();

            setupQueues();

            // Initialize Bull Dashboard BEFORE routes to avoid auth middleware
            initializeBullDashboard(app);

            setupRoutes();

            setupErrorHandlers();

            console.log(
                '\nWe are listening for requests... 📡 📡 📡 at ' +
                    `http://localhost:${process.env.PORT}`
            );
        })
        .catch((e) => {
            console.log(e);
        });
};

connectToDBReplicaAndStart = () => {
    if (process.env.DB_URL_REPLICA) {
        massive(connectionStringReplica)
            .then((massiveReplicaInstance) => {
                console.log('Replica Database connection is established 👍');
                app.set(`db_replica`, massiveReplicaInstance);
            })
            .catch((e) => {
                console.log(e);
            });
    } else {
        console.log('Missing ENV variable -> DB_URL_REPLICA');
    }
};

connectToDBDumpAndStart = () => {
    if (process.env.DB_URL_DUMP) {
        massive(connectionStringDump)
            .then((massiveDumpInstance) => {
                console.log('Dump Database connection is established 👍');
                app.set(`db_dump`, massiveDumpInstance);
            })
            .catch((e) => {
                console.log(e);
            });
    } else {
        console.log('Missing ENV variable -> DB_URL_DUMP');
    }
};

connectToFirebaseAdmin = () => {
    const fcm_cred_config = process.env.FCM_CRED_CONFIG;
    if (fcm_cred_config) {
        const admin = require('firebase-admin');
        const databaseURL = process.env.FIREBASE_DATABASE_URL;
        admin.initializeApp({
            credential: admin.credential.cert(
                JSON.parse(process.env.FCM_CRED_CONFIG)
            ), // Replace with your service account key path
            databaseURL,
        });
        app.set(`firebase_admin`, admin);
    }
};

module.exports = app;
