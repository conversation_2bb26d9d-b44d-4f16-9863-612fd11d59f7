select json_build_object(
$$Request ID$$,srvc_req.display_code , $$Service Type$$ ,srvc_type.title, $$Task start date$$ ,DATE((sbtsk.start_time)::timestamp at time zone $$utc$$ at time zone $$Asia/kolkata$$), $$Assignee name$$ ,assignee."name", $$Task Status$$ ,sbtsk_status.title, $$Assigned by$$ ,assigned_by."name", $$Site location group$$ ,tms_hlpr_get_loc_grps_name(952, srvc_req, $$[{"id": 236, "group_data": {"cities": [], "states": [], "pincode": ["744302", "744207", "744206", "744303", "744205", "744112", "744304", "744210", "744102", "744204", "744203", "744209", "744106", "744301", "744202", "744103", "744105", "744104", "744101", "744201", "744211", "744107", "516218", "522310", "533236", "535579", "533254", "517001", "535522", "518301", "522549", "522403", "523371", "524318", "517408", "523187", "532211", "523280", "535546", "518385", "535148", "518176", "515803", "534230", "517193", "533214", "533285", "516172", "516175", "532242", "521107", "531113", "533297", "524313", "515291", "523171", "523224", "518135", "533126", "524234", "521178", "530014", "534235", "533261", "515411", "516237", "516293", "524343", "523225", "534209", "531087", "524312", "518003", "532457", "533294", "516217", "518347", "524413", "524403", "516215", "524004", "517305", "517414", "534301", "522439", "521001", "523135", "522647", "535126", "521121", "522616", "515212", "516002", "518225", "534275", "523274", "521332", "517257", "521149", "516129", "531173", "521226", "521148", "534236", "524307", "518553", "524409", "533340", "515871", "531036", "520012", "522315", "532459", "517587", "516380", "522213", "533234", "516105", "516259", "518468", "532190", "515521", "535218", "518155", "515331", "534134", "532429", "531032", "516152", "534217", "532284", "517125", "531117", "523230", "516004", "524302", "518465", "516267", "522259", "523002", "533346", "521390", "517112", "530016", "533434", "518005", "518510", "522614", "523223", "532408", "517214", "523286", "521105", "533106", "531033", "534239", "516355", "516432", "523184", "517391", "535183", "533016", "515311", "518101", "515721", "532430", "518396", "521215", "530005", "530015", "521344", "534281", "521165", "533215", "534452", "518513", "523105", "531085", "531118", "534401", "516101", "523357", "516247", "523281", "521213", "534004", "521235", "534101", "517417", "531034", "524142", "530026", "522613", "522101", "517235", "524317", "534260", "507130", "534244", "522402", "518511", "534218", "524224", "523271", "515561", "517418", "535002", "522435", "534210", "523180", "522421", "534425", "533468", "517641", "534316", "522257", "515002", "524225", "534460", "533216", "534305", "523001", "523302", "531026", "524345", "522601", "532484", "523246", "515763", "524366", "533217", "518218", "516104", "532410", "524132", "533238", "533252", "534250", "532402", "530031", "520013", "522409", "523201", "522611", "518010", "520002", "530024", "533287", "517247", "522649", "523155", "516270", "523228", "522502", "515211", "534331", "521106", "533343", "516474", "521133", "521340", "522438", "522304", "522324", "521153", "518323", "517506", "522408", "533235", "523167", "518348", "533220", "516421", "518346", "515402", "532428", "532220", "531023", "522414", "524316", "517642", "524404", "517592", "531084", "515775", "515511", "534196", "533255", "522401", "516502", "516434", "517415", "534302", "534204", "533107", "534269", "521286", "524421", "535006", "522503", "532219", "522415", "532401", "535281", "532215", "534455", "534003", "534329", "535593", "515787", "534338", "531001", "523109", "518206", "533407", "523116", "516310", "523330", "535594", "535525", "533464", "530043", "515201", "517640", "533124", "532264", "524152", "523108", "523253", "530035", "515405", "523328", "535204", "521227", "533263", "516444", "523104", "534006", "522603", "522202", "533428", "517526", "518412", "523320", "517297", "535526", "533463", "535280", "518593", "533250", "523185", "524002", "518405", "534451", "521207", "534326", "534122", "515415", "534216", "524402", "521158", "516504", "522325", "521330", "517123", "521325", "533260", "517586", "518512", "518395", "518220", "535568", "515865", "534238", "532474", "518122", "522265", "534145", "535501", "535160", "521333", "521132", "535250", "533305", "518502", "524414", "516360", "516311", "530029", "515867", "516321", "523273", "515110", "521322", "535128", "516108", "515766", "533295", "533247", "524415", "515122", "524240", "518508", "533213", "533408", "524123", "532212", "533344", "534211", "515722", "532404", "524405", "533240", "521101", "521328", "521311", "534247", "523334", "530051", "534146", "515551", "522660", "522034", "515133", "523110", "515571", "532426", "515870", "531040", "530041", "517571", "518422", "534198", "521162", "521321", "533290", "522001", "533241", "518123", "517590", "534240", "533229", "515425", "524221", "522305", "516349", "522657", "517425", "518467", "530028", "522124", "523301", "522509", "516309", "523190", "533232", "515286", "521260", "517194", "517129", "531133", "516127", "518166", "518573", "535216", "522437", "516150", "523329", "535004", "522261", "524137", "517130", "532445", "520001", "521202", "515134", "517325", "534456", "515651", "518124", "518501", "535463", "523279", "517582", "524401", "522314", "534225", "531162", "521214", "533431", "522201", "534342", "515581", "515003", "517432", "516151", "534442", "521110", "521175", "530044", "516339", "531020", "523168", "533003", "533004", "523252", "533339", "531114", "521457", "535124", "522006", "533342", "531029", "518466", "534416", "534007", "531115", "523254", "523335", "532263", "515241", "515159", "524005", "515271", "535240", "523373", "517584", "522016", "515144", "523211", "530002", "516361", "517126", "522659", "521324", "522111", "515164", "522237", "515741", "523263", "532312", "534245", "517280", "523316", "516464", "517591", "530009", "516001", "520011", "533237", "517581", "518001", "533483", "515631", "523356", "522235", "533430", "522009", "534312", "530048", "521002", "524121", "515321", "534330", "521157", "524101", "531021", "524201", "515765", "535101", "521181", "524127", "517588", "524411", "530040", "524308", "531002", "531219", "516505", "517505", "524227", "533292", "523111", "517004", "516126", "532458", "521139", "533577", "516289", "535273", "515455", "531060", "532407", "533210", "524322", "530046", "518462", "523117", "520008", "523114", "522268", "524304", "535213", "531011", "534111", "515556", "533286", "517403", "517426", "524342", "535581", "532148", "515005", "521108", "522020", "532406", "524321", "535580", "515401", "515863", "518102", "523369", "522236", "532195", "515231", "524203", "531031", "530027", "533248", "522002", "531061", "533101", "518344", "517569", "516213", "534427", "533461", "521228", "522617", "534313", "534002", "524410", "533253", "517291", "516268", "522663", "516228", "516216", "517102", "531025", "517326", "517416", "530068", "535145", "522015", "518464", "532455", "533104", "534341", "523327", "515202", "518004", "535527", "534124", "530004", "534437", "534268", "533251", "534197", "530018", "523346", "518432", "515601", "522007", "516163", "532432", "533274", "522258", "535534", "516130", "535559", "522019", "524223", "517152", "521212", "518002", "521256", "533242", "523182", "532443", "522501", "518333", "522005", "534328", "533264", "515413", "516431", "518345", "533218", "517645", "534207", "523265", "517234", "515154", "532456", "522329", "521241", "530045", "515501", "518380", "522612", "524129", "515842", "517644", "523292", "515671", "522411", "518217", "521136", "521137", "524001", "531030", "523115", "524408", "522018", "535001", "515445", "516411", "517370", "518599", "515711", "515123", "521312", "522308", "532005", "516193", "533293", "532222", "522233", "534318", "516359", "522312", "524344", "533291", "517541", "524124", "517131", "521130", "516454", "530022", "534324", "534475", "533444", "515305", "530017", "535558", "516439", "517237", "515661", "515761", "524305", "516503", "532123", "534001", "518222", "530011", "535125", "533249", "517350", "518221", "523331", "522646", "521150", "535003", "531055", "523261", "533221", "517213", "534461", "517589", "516401", "521345", "533450", "524309", "521104", "535547", "521120", "523270", "522426", "523368", "533307", "524134", "530008", "516110", "534448", "522318", "520007", "534166", "522341", "534350", "517536", "517561", "523304", "533341", "533103", "523370", "518006", "516115", "517583", "535221", "534195", "534447", "522017", "522330", "533432", "518313", "518007", "521225", "533462", "531024", "523213", "517352", "515101", "523247", "523336", "523303", "533437", "516390", "532213", "515301", "520004", "531022", "518563", "517507", "534467", "533212", "533228", "518350", "521402", "517643", "534432", "522309", "530053", "522003", "535557", "524236", "532122", "532290", "523113", "518343", "517101", "517599", "533406", "521180", "523227", "533446", "524323", "531081", "535260", "521369", "516227", "521343", "533435", "532243", "530013", "523332", "518463", "534222", "515774", "532421", "533239", "531126", "521250", "535591", "518302", "521211", "532409", "515701", "516329", "517277", "523272", "517620", "521135", "521403", "521401", "515751", "517551", "516107", "515281", "534449", "521003", "532218", "518165", "522508", "521366", "532185", "515124", "534201", "523170", "522658", "516173", "533284", "523101", "518216", "534123", "524310", "523291", "533262", "517351", "518533", "515822", "517423", "522410", "521131", "522301", "532427", "532201", "533288", "515465", "521331", "532216", "523112", "515801", "535161", "533222", "533201", "530032", "531105", "535214", "521134", "522306", "523315", "524230", "524131", "515261", "522212", "521301", "515414", "518523", "523226", "533447", "523264", "516312", "522413", "521247", "518308", "521323", "517002", "517419", "522626", "517429", "534311", "520010", "532403", "534340", "532322", "534411", "533211", "517299", "518594", "532186", "534176", "524311", "524228", "521122", "532221", "521230", "531035", "533223", "516003", "534199", "515611", "516162", "518411", "515004", "533309", "522004", "534186", "521326", "522317", "521170", "515872", "535578", "523169", "515001", "521261", "518134", "524346", "523183", "518186", "522313", "523305", "532462", "521201", "517236", "522311", "533102", "532425", "535217", "534165", "521109", "516484", "523165", "531019", "534406", "518112", "521456", "524314", "534280", "516214", "530001", "535215", "516391", "531083", "534208", "533289", "502355", "523367", "522510", "530052", "532291", "522615", "518674", "521190", "534156", "535521", "532214", "533429", "532168", "517390", "531111", "533244", "524226", "516350", "531027", "517127", "517124", "521246", "516396", "522256", "515767", "532203", "515531", "523157", "517619", "516269", "518583", "521125", "515641", "523156", "534265", "516330", "534215", "524239", "535524", "521138", "517421", "531116", "515341", "533401", "518442", "522307", "521163", "517502", "518390", "522619", "532440", "517501", "530020", "520003", "515303", "533433", "521229", "523181", "521164", "517128", "530003", "522211", "523241", "535573", "534112", "516433", "518401", "517167", "522262", "515812", "515621", "522112", "515541", "523245", "516233", "521102", "524003", "521329", "524222", "522264", "517424", "524341", "533006", "534450", "522436", "535102", "531149", "523372", "517319", "530049", "531127", "533345", "521356", "518145", "534315", "517504", "531163", "522303", "517172", "518543", "531077", "532127", "534426", "531082", "523166", "522661", "515408", "523240", "515832", "524406", "517192", "524319", "521126", "521182", "524412", "523326", "517503", "521263", "523333", "532405", "522102", "534202", "524301", "533005", "531028", "534267", "532001", "524303", "518349", "531075", "534320", "535592", "522316", "521156", "533448", "517422", "534327", "533306", "534126", "532461", "516203", "522113", "524315", "533445", "523212", "530047", "532460", "515672", "515435", "534206", "522234", "530012", "524126", "533233", "523186", "533001", "518452", "518598", "523214", "534462", "516356", "518196", "533002", "523260", "517113", "535005", "524306", "531151", "524320", "522302", "516128", "522412", "521245", "521327", "534102", "517401", "522529", "520015", "521151", "533125", "517132", "534237", "533256", "515731", "521185", "516501", "535270", "524407", "516257", "535220", "515591", "521111", "535502", "533105", "517520", "533436", "532292", "533308", "534243", "533449", "518360", "516362", "530007", "521183", "523262", "534227", "535523", "534266", "533440", "534005", "576214", "574239", "581320", "571106", "561205", "581195", "591315", "573214", "571450", "572114", "586103", "571123", "581338", "591123", "585306", "562107", "590011", "581106", "574102", "577115", "581351", "572225", "574212", "574285", "574150", "583130", "560005", "583279", "585402", "591224", "577501", "585323", "581340", "581197", "562114", "560054", "574210", "585211", "571214", "572139", "577547", "580002", "560103", "571119", "577413", "577529", "580032", "571433", "574211", "577521", "582209", "581365", "572142", "574325", "571107", "583101", "583224", "581358", "560013", "573112", "591219", "572212", "583216", "591305", "586214", "561101", "573220", "571405", "570016", "591240", "585216", "590019", "583213", "572221", "560021", "561211", "573116", "570015", "586108", "577146", "581344", "563121", "581210", "573216", "583275", "560055", "562130", "591201", "580114", "571435", "562123", "577138", "585213", "571811", "573202", "581187", "563128", "577528", "591228", "571312", "560109", "574225", "574234", "591313", "574326", "591345", "577182", "570022", "577130", "585324", "560060", "583221", "577532", "571403", "573225", "571421", "577117", "560009", "583103", "581303", "587314", "577175", "591213", "583219", "586207", "573115", "571103", "573120", "560008", "563101", "576230", "576234", "560017", "584122", "581206", "560086", "574241", "577552", "560067", "577453", "587121", "576229", "560039", "561201", "563103", "583111", "577451", "573150", "590009", "576213", "577546", "580026", "591304", "581401", "570030", "571234", "577215", "576114", "562157", "560002", "573144", "581332", "583121", "560027", "584134", "573215", "577557", "560072", "583123", "560042", "577203", "575020", "574224", "583131", "563137", "577243", "561208", "572115", "577596", "583230", "574229", "582118", "577121", "571430", "584202", "561202", "560018", "562126", "574198", "585105", "577536", "585318", "583126", "577113", "562103", "560091", "585291", "584104", "577426", "574114", "571216", "585304", "577599", "577180", "587116", "585353", "571429", "581111", "563102", "580009", "576104", "571443", "587312", "591303", "571448", "587124", "560064", "581345", "572117", "560041", "585227", "561206", "591226", "577120", "581402", "571122", "571606", "577160", "574219", "585226", "560102", "591346", "576111", "562109", "577145", "586101", "577231", "591125", "560073", "581411", "591247", "571121", "573130", "583113", "581363", "560071", "587114", "577412", "581314", "581354", "584128", "577101", "581350", "577225", "585290", "577232", "576282", "577543", "581423", "577114", "585221", "560097", "560011", "571423", "570008", "591242", "573118", "573127", "571438", "581204", "577301", "570028", "560099", "571475", "577522", "581202", "577434", "581324", "582112", "576247", "560019", "591117", "577132", "581193", "563119", "577514", "570025", "577002", "572106", "585311", "573111", "575017", "585214", "573101", "591218", "586208", "585225", "570009", "574243", "581148", "577431", "591317", "577230", "577535", "577220", "583276", "581118", "591301", "571218", "560112", "591344", "584129", "581403", "581108", "581317", "581348", "570019", "562122", "571125", "583235", "591232", "577135", "581115", "584170", "591340", "585104", "587120", "560089", "560104", "577422", "572141", "572102", "571313", "574222", "574267", "571463", "571311", "581207", "585222", "572136", "574199", "582204", "577126", "585444", "590008", "591116", "586117", "585418", "585237", "585443", "585303", "560043", "585355", "571436", "581306", "560056", "591211", "576212", "581453", "560105", "574112", "577003", "571236", "591310", "571315", "571126", "581120", "574144", "560028", "575018", "573219", "572220", "581107", "591225", "571108", "574314", "563116", "591287", "571189", "585302", "581205", "571425", "562121", "573137", "586119", "573124", "581353", "577213", "573141", "562129", "581440", "560065", "583280", "581384", "591103", "571124", "575014", "576211", "583137", "571187", "583132", "571490", "571419", "585331", "572105", "560093", "574146", "587315", "587204", "576216", "581128", "571610", "591173", "591114", "577112", "575023", "586122", "591106", "575029", "587203", "583239", "573165", "562149", "577229", "577124", "572201", "577127", "574105", "581201", "563162", "571440", "581316", "571212", "570010", "560025", "590005", "560075", "563146", "574509", "583237", "577227", "587316", "585315", "560080", "576257", "580008", "581103", "573131", "575030", "577526", "573211", "577004", "574142", "571109", "573129", "583136", "583283", "590010", "577001", "574259", "576215", "562164", "587111", "562135", "581396", "571426", "577222", "574119", "585330", "562131", "572217", "577551", "577211", "581117", "571342", "586128", "575019", "574221", "583236", "591221", "574101", "573113", "591217", "576103", "574203", "577116", "574227", "563160", "571404", "574232", "583212", "571117", "577005", "583228", "587313", "574235", "581346", "577129", "585403", "571444", "571247", "574240", "585325", "576235", "585307", "591127", "560070", "576121", "591243", "591119", "583232", "583117", "571130", "560016", "581129", "584102", "577133", "591234", "571231", "587154", "587101", "560088", "583104", "573134", "577102", "574116", "562160", "571416", "562110", "561212", "585321", "581304", "571424", "584125", "581105", "577524", "582120", "587102", "573217", "577534", "585308", "591241", "585210", "591110", "571248", "574151", "587118", "560052", "586201", "571442", "581352", "581322", "561209", "560074", "577419", "560049", "560036", "591314", "571301", "560010", "577448", "586203", "583135", "585217", "590001", "583116", "576124", "583226", "583233", "586125", "563136", "581359", "587201", "582102", "581109", "577523", "584111", "582117", "562106", "563118", "563115", "574231", "577418", "563131", "576217", "577224", "574244", "577137", "587155", "561203", "585326", "573126", "591107", "581301", "577139", "574213", "575001", "562108", "574117", "572119", "585411", "591147", "571237", "571802", "570018", "577429", "585329", "570007", "585401", "560038", "580118", "582114", "582201", "563130", "591263", "591220", "572125", "560048", "577520", "574129", "560026", "591223", "585236", "577006", "585265", "571617", "574108", "560050", "571254", "581121", "586210", "581421", "567087", "591237", "581196", "577598", "586215", "572111", "560030", "585317", "583105", "581329", "590016", "571807", "577223", "562163", "577553", "591231", "581186", "572126", "585413", "560094", "577513", "585320", "580021", "591244", "577401", "563132", "591254", "577537", "571102", "571457", "583119", "584127", "570006", "591102", "572138", "577179", "576218", "590014", "575008", "587205", "577544", "582208", "570001", "560046", "574153", "583120", "572175", "571428", "577111", "571603", "572130", "571427", "583234", "571128", "585218", "581112", "571201", "562159", "573213", "577519", "574236", "574115", "577125", "585287", "560092", "577140", "581315", "591101", "572132", "577555", "560058", "583217", "560084", "571251", "591306", "571250", "583218", "585367", "577558", "580023", "576222", "584139", "591131", "572121", "574111", "591109", "577566", "575007", "583238", "573128", "587202", "591108", "587117", "572223", "591112", "571115", "591236", "583229", "591120", "591153", "574107", "586202", "570026", "577142", "586111", "581116", "585316", "575025", "580025", "560040", "574237", "560006", "583122", "582203", "585436", "587206", "591124", "571812", "560012", "591216", "580028", "586124", "560107", "590018", "584138", "574122", "590015", "586130", "577214", "560108", "581341", "574109", "561213", "560003", "575022", "571605", "574154", "560062", "576108", "574118", "584135", "571455", "577554", "560037", "582101", "581357", "591128", "583128", "574313", "580005", "571302", "573210", "577511", "577430", "572213", "573218", "577589", "590003", "560053", "585224", "576117", "585215", "560096", "572140", "577542", "580029", "574226", "577541", "563124", "577427", "583201", "577428", "571114", "586116", "561228", "583125", "583127", "590006", "580024", "562111", "571601", "577597", "581327", "581326", "576225", "581349", "573123", "571431", "584113", "576232", "584116", "562117", "560098", "571316", "574106", "585313", "573142", "591126", "575005", "585412", "586114", "563126", "560007", "582103", "577134", "577538", "591111", "591230", "581101", "577423", "582115", "570012", "581412", "577512", "574216", "581330", "577416", "574217", "572214", "573162", "571219", "576113", "574197", "560014", "583225", "577436", "585301", "577228", "577421", "574230", "591115", "580020", "575002", "580031", "581336", "586120", "586109", "574220", "563134", "571101", "581334", "577548", "583268", "562127", "572227", "560085", "560300", "586127", "582202", "587115", "591307", "577233", "591143", "591316", "571215", "575011", "591104", "573102", "562102", "576221", "584133", "583220", "574110", "585102", "560057", "563135", "581119", "587301", "571446", "591130", "582207", "563138", "562112", "586115", "583124", "572168", "585310", "571402", "576228", "591212", "573117", "571445", "583223", "575004", "571439", "587207", "570029", "585328", "591229", "574228", "577201", "574145", "571418", "583282", "584132", "571211", "585322", "576210", "576102", "587330", "572219", "583118", "563159", "563125", "577545", "586204", "587125", "581361", "572104", "584103", "571110", "585212", "572103", "577550", "577411", "571104", "585421", "560015", "573121", "575028", "577435", "573125", "581110", "591156", "577516", "560079", "581325", "580007", "584203", "571252", "581123", "571432", "562119", "576112", "581102", "586113", "584167", "574218", "577549", "577302", "570027", "571604", "574265", "583278", "571249", "581360", "570011", "587104", "581355", "586217", "573164", "577204", "583214", "581356", "573201", "572128", "571478", "572224", "587119", "577533", "577205", "560034", "586123", "576226", "560082", "576283", "571235", "586112", "582111", "586129", "571253", "570003", "571127", "570057", "580011", "574113", "574260", "582210", "571602", "562125", "560024", "563114", "585415", "576106", "572123", "563113", "571441", "585419", "577540", "585229", "573122", "582206", "572226", "581198", "563117", "562128", "570005", "591238", "577433", "573119", "582116", "584118", "583102", "586212", "576101", "577601", "590098", "585103", "577128", "576115", "577417", "571415", "574253", "581337", "583152", "572118", "577425", "585228", "583215", "581347", "574324", "591227", "572129", "584136", "560090", "590017", "577202", "562162", "574238", "560078", "580006", "572101", "585417", "575006", "571401", "560023", "581318", "571607", "577527", "584120", "577530", "563127", "571422", "580001", "560081", "562138", "563161", "591118", "577414", "591246", "574327", "574233", "583114", "580004", "591302", "591308", "575016", "581339", "573136", "571314", "572113", "571134", "577515", "576201", "577432", "573133", "585101", "577219", "591113", "591312", "583211", "574201", "587112", "576219", "577556", "577131", "572116", "576233", "576107", "577415", "583115", "586209", "584101", "585327", "586118", "572133", "572135", "586121", "585312", "590020", "585106", "570020", "581323", "585414", "586216", "591309", "581362", "572211", "577226", "560100", "580112", "585201", "572122", "587113", "571118", "574242", "591129", "576120", "574223", "560033", "573212", "577539", "581335", "585437", "583277", "560059", "580030", "584143", "575013", "573103", "563120", "582211", "575010", "581104", "574248", "562161", "586211", "581209", "563139", "574328", "570023", "577531", "563129", "584124", "577136", "581342", "571105", "577217", "585223", "560022", "581333", "560068", "576220", "563133", "586206", "570031", "582205", "581319", "574214", "560110", "561204", "575003", "581302", "560047", "571417", "585305", "587311", "571213", "572107", "581450", "581307", "571232", "562105", "591311", "591265", "563123", "574103", "591248", "581328", "582113", "586205", "585219", "583231", "577181", "591222", "560083", "581305", "583203", "581126", "581203", "560095", "581321", "561207", "574143", "572124", "591235", "576231", "572215", "585314", "581331", "572127", "571434", "576122", "574279", "560076", "591233", "584140", "591136", "577168", "576223", "585220", "572216", "576227", "577144", "560020", "575015", "584126", "583281", "560051", "574104", "586104", "584115", "572137", "574148", "570017", "560035", "560004", "581308", "586213", "560077", "577424", "583222", "587103", "577517", "570002", "577216", "587122", "571111", "571116", "572120", "574202", "563122", "585292", "577525", "584123", "577245", "581208", "574274", "562104", "572134", "591239", "571320", "562132", "560045", "574323", "581400", "581211", "585319", "585416", "571217", "581212", "560061", "577518", "585202", "577502", "572222", "560001", "560087", "577221", "576105", "570004", "591121", "591122", "583227", "574141", "570014", "571129", "581343", "562101", "573135", "581145", "576224", "572228", "572218", "571477", "571476", "583112", "582119", "577118", "580003", "577218", "561210", "583134", "562120", "586102", "571120", "583129", "591214", "560032", "560063", "581213", "577452", "591215", "585309", "581113", "560029", "577122", "572112", "560066", "577123", "685615", "673009", "682023", "689662", "695612", "690548", "680685", "695506", "679532", "691019", "695610", "685581", "679102", "673574", "691309", "691536", "685571", "671321", "685507", "689694", "680655", "695607", "689678", "686532", "679574", "689106", "683521", "689506", "673002", "685620", "695318", "686518", "670622", "686636", "679551", "676306", "683563", "686018", "695611", "682316", "670646", "689582", "680551", "686542", "673570", "680021", "683112", "682012", "671315", "676301", "670017", "695008", "686613", "680594", "678703", "682034", "686605", "676506", "690525", "695146", "676523", "673103", "689543", "686630", "690571", "680670", "673121", "671351", "695304", "673019", "695004", "686585", "689589", "685585", "679323", "673526", "678502", "690510", "685515", "673582", "678705", "680101", "689602", "670621", "689571", "678531", "679503", "673634", "685535", "690533", "670334", "682024", "691302", "690561", "695606", "683556", "695133", "682006", "670501", "695141", "689695", "678103", "691507", "671316", "673613", "690524", "679333", "679101", "689611", "689115", "695028", "678633", "695121", "690520", "680512", "690559", "686539", "680724", "686671", "673317", "686667", "670502", "689674", "691559", "670612", "673017", "689647", "679531", "683580", "680004", "689104", "682005", "695014", "683502", "678702", "691016", "686021", "686520", "685563", "689597", "678004", "689586", "680732", "685618", "686544", "679502", "670107", "690105", "695009", "686561", "688004", "670705", "689545", "678508", "670703", "686503", "679577", "678534", "673101", "695018", "673102", "679357", "695002", "673312", "686652", "680002", "671541", "695605", "691557", "682305", "683549", "673032", "689512", "673593", "679326", "680028", "686579", "673655", "689624", "678622", "683543", "676108", "695588", "673505", "670014", "688502", "676305", "689105", "680103", "689652", "689505", "689675", "695563", "678507", "689122", "686534", "680741", "686508", "686574", "690502", "690547", "686505", "695589", "695043", "695021", "673571", "690532", "695145", "680612", "691589", "680516", "688538", "673596", "679505", "689503", "690519", "690514", "680582", "682016", "691572", "686103", "690539", "673008", "678595", "673016", "690529", "679572", "679501", "673631", "689514", "680543", "678013", "679103", "679571", "676317", "680689", "673604", "695040", "673006", "686601", "689121", "686560", "695526", "673304", "679341", "680722", "680020", "679516", "680588", "683517", "695564", "688525", "673007", "673502", "691521", "680631", "686104", "685595", "678624", "688007", "691511", "695541", "670672", "680683", "678003", "679301", "670102", "673105", "679122", "680617", "680604", "686008", "691005", "695001", "695306", "691301", "689572", "686548", "678505", "678542", "679511", "678594", "683594", "695313", "680545", "680311", "686580", "676562", "682501", "670010", "688526", "679338", "691021", "695522", "682312", "691522", "679578", "695585", "679332", "686009", "680721", "680121", "689648", "688534", "695572", "689626", "682314", "688002", "670692", "690544", "695303", "670012", "679552", "685505", "695029", "680571", "678701", "680514", "683522", "695301", "685606", "691601", "670325", "682509", "690534", "670631", "690531", "680010", "686541", "670721", "670143", "688555", "689513", "686653", "680519", "676508", "683579", "686672", "678683", "689532", "689111", "691552", "695581", "678688", "689501", "673615", "673513", "673001", "682008", "678704", "695504", "671533", "690104", "686105", "685582", "671324", "680620", "685553", "690102", "680682", "689592", "680523", "676561", "682007", "689112", "683104", "678641", "682018", "686681", "678722", "679105", "683576", "689585", "673308", "682025", "676503", "680642", "690103", "691537", "673106", "695603", "676105", "678102", "686583", "691534", "682027", "686521", "673577", "680699", "673585", "695604", "686547", "679591", "685602", "680663", "680665", "691504", "680589", "691571", "691333", "691524", "695571", "673528", "682031", "673104", "670327", "679580", "673522", "670601", "686604", "691500", "676507", "683577", "695561", "678532", "680008", "682310", "670005", "686016", "691020", "689547", "680123", "680522", "673328", "688001", "689581", "683520", "671124", "683542", "685531", "673521", "691520", "678011", "678583", "688013", "670141", "682048", "676505", "679563", "688528", "688530", "686631", "695503", "695317", "678553", "690509", "690537", "689673", "691525", "673015", "689103", "670694", "695126", "670591", "671323", "689622", "683562", "679304", "678681", "682009", "682504", "695015", "680587", "682032", "673123", "673018", "682303", "686102", "671317", "678571", "670503", "695026", "685566", "690521", "695042", "691590", "679582", "679514", "689663", "682013", "695125", "680304", "679583", "689643", "689641", "678601", "695505", "691332", "689541", "695020", "686573", "691515", "682315", "683585", "682039", "680569", "689573", "686563", "689693", "690106", "678682", "676517", "683573", "679337", "678554", "682020", "670650", "671348", "679104", "685609", "686516", "678556", "691509", "686522", "689650", "689621", "695311", "682001", "673576", "695033", "686651", "679123", "690542", "689508", "686006", "689110", "691531", "680664", "670581", "676551", "682502", "689584", "682304", "690108", "680305", "671313", "673637", "682508", "680125", "686602", "673029", "680563", "676304", "685565", "680317", "686612", "691566", "686005", "680307", "688524", "673004", "680521", "682036", "680524", "671311", "673005", "673003", "691319", "691322", "695305", "686014", "678597", "671122", "695038", "691305", "673503", "686633", "678573", "679586", "678642", "680553", "690107", "683105", "680566", "680104", "690516", "695142", "685501", "695011", "685607", "680515", "680652", "670593", "686666", "680733", "670595", "679307", "680565", "695044", "670645", "691510", "689612", "685509", "688011", "689671", "695310", "685514", "695034", "683511", "695525", "689625", "683545", "695502", "695022", "670691", "673635", "670353", "671123", "689510", "680022", "685552", "670007", "683571", "686003", "686661", "690523", "695023", "678545", "676309", "680508", "678552", "673602", "688523", "686013", "670633", "683561", "689550", "680712", "695312", "673524", "670009", "671322", "695583", "673586", "678685", "680014", "679585", "678014", "686007", "673542", "690572", "695104", "688561", "690530", "686668", "680611", "679584", "679321", "695587", "689583", "682030", "688503", "671318", "673581", "678504", "679553", "689502", "679587", "691002", "695316", "686581", "689614", "686010", "682511", "678582", "679512", "680616", "683581", "689661", "686020", "679562", "682042", "678544", "689520", "678007", "678008", "695102", "691512", "686514", "670101", "695562", "691579", "679554", "678005", "688541", "689102", "683575", "686673", "678731", "689533", "673525", "673641", "679533", "686536", "670142", "673021", "686540", "673309", "680681", "689113", "680507", "670306", "689623", "685584", "685613", "670358", "686022", "679535", "683515", "676528", "689544", "679329", "695307", "691535", "695030", "689613", "678651", "679308", "686515", "678592", "683565", "695524", "679331", "691543", "695006", "686017", "686608", "678686", "682313", "680302", "689511", "682309", "686141", "686122", "690574", "695099", "680584", "679106", "678732", "683544", "683541", "673614", "670305", "683512", "673611", "686691", "695513", "686616", "679576", "680001", "682306", "676121", "670643", "680568", "679335", "695122", "601021", "686012", "686669", "686001", "670663", "679581", "689698", "691508", "685588", "680590", "673508", "695508", "680306", "689587", "671121", "691311", "670649", "680562", "690503", "682503", "673620", "686146", "686572", "678581", "680513", "695003", "683574", "680561", "680585", "691304", "670611", "673316", "691538", "689711", "688006", "680671", "680011", "680701", "676103", "690504", "670562", "673523", "688562", "670671", "685614", "680309", "673645", "691506", "690517", "689645", "670308", "691331", "685605", "691556", "679334", "686517", "673504", "670741", "678661", "686662", "670651", "688012", "691555", "686637", "670563", "680654", "686663", "670309", "670106", "678541", "695035", "695547", "686607", "673640", "670105", "691573", "678687", "691577", "691503", "691502", "689668", "688539", "679504", "690522", "691540", "688540", "685589", "688014", "670006", "691501", "678684", "691584", "689646", "620680", "678611", "670003", "686577", "683572", "671542", "690507", "680005", "690558", "673303", "686535", "680506", "691306", "691003", "686571", "686002", "679340", "680668", "678012", "695143", "683518", "680517", "678613", "676101", "673501", "680504", "683501", "686586", "688532", "686015", "683513", "680731", "673642", "695007", "680308", "685608", "682040", "682505", "685612", "685551", "673012", "689627", "680751", "695523", "689574", "691307", "679575", "685562", "680544", "689542", "688531", "676319", "686509", "670706", "670602", "683550", "680619", "680301", "682019", "679521", "670702", "680503", "691014", "691516", "670521", "683546", "673639", "695614", "688008", "689676", "673591", "691581", "683108", "689124", "695574", "686538", "695025", "670644", "678555", "670693", "695501", "695608", "686143", "691008", "685603", "678543", "678533", "679339", "680669", "683101", "683587", "676307", "676102", "689108", "685532", "680567", "670592", "688501", "678557", "680667", "689531", "676552", "680102", "673616", "686634", "670511", "682011", "686502", "695101", "686511", "670642", "680027", "680596", "679522", "690506", "685508", "673020", "673311", "682035", "695568", "676303", "686507", "689595", "686578", "691303", "678721", "688522", "676525", "695527", "680702", "673572", "673011", "686531", "690536", "680009", "680602", "670641", "678572", "689548", "680122", "679513", "680697", "678551", "689507", "691578", "691334", "688570", "679302", "691554", "686611", "678501", "676109", "671543", "676501", "678621", "679336", "686582", "691004", "685533", "679325", "673507", "680312", "689101", "686101", "691012", "676553", "682021", "690512", "670301", "673529", "673541", "691541", "670613", "686506", "680666", "685601", "691532", "670013", "676106", "678506", "682010", "680007", "680591", "686546", "680686", "686603", "686011", "670561", "686606", "673509", "688504", "673314", "673305", "678010", "690527", "680601", "670001", "695584", "680564", "691001", "689656", "682017", "695602", "690573", "680552", "679579", "680013", "689667", "689549", "682311", "695551", "671314", "680623", "690535", "673632", "680711", "685619", "689672", "691602", "691526", "670011", "685583", "689504", "670103", "680555", "691009", "671531", "691523", "680310", "691574", "695032", "680653", "670008", "683503", "682003", "695543", "683110", "680012", "678001", "682041", "678512", "691015", "686610", "686144", "686504", "690505", "685554", "691583", "682308", "683111", "673014", "695528", "695132", "676519", "676522", "689590", "670303", "676510", "686513", "670302", "678762", "680688", "671532", "686576", "686537", "686519", "682307", "691310", "673010", "682002", "680661", "678546", "683578", "670704", "680613", "685503", "680586", "676311", "689664", "682301", "690110", "676504", "673579", "673647", "686545", "695302", "691007", "688536", "695615", "680684", "680006", "679305", "680501", "691010", "670018", "673638", "695309", "691013", "695005", "670304", "690513", "671132", "680303", "676542", "678596", "689126", "682022", "695601", "690518", "680518", "676302", "678671", "695010", "670104", "685591", "695582", "673592", "686510", "673636", "686670", "690511", "695103", "679327", "688521", "686123", "689649", "691585", "691505", "689653", "680615", "695609", "685561", "671310", "688009", "670307", "679309", "682037", "670504", "691011", "683547", "680703", "670310", "689654", "676521", "679121", "676312", "670676", "686575", "673595", "695308", "695512", "685587", "683102", "679313", "670594", "680662", "686106", "680570", "683516", "673603", "690508", "682302", "679324", "680651", "688535", "673028", "689696", "688506", "683514", "680641", "679536", "695123", "678101", "691582", "695024", "686693", "689509", "680541", "676320", "689615", "676107", "673527", "694003", "670004", "679534", "689123", "673027", "679506", "690101", "691533", "680691", "678623", "670331", "673612", "680656", "695144", "671312", "685512", "671552", "670632", "686609", "691530", "689691", "670582", "695036", "695575", "670002", "671319", "686543", "689594", "678593", "689642", "673302", "690501", "682506", "683106", "679322", "695134", "689699", "686019", "680505", "679523", "695124", "689521", "679564", "673580", "678612", "673313", "678706", "682028", "691576", "673661", "671551", "689551", "680510", "695019", "695586", "679561", "680546", "673122", "676502", "673306", "686512", "689588", "691553", "676541", "683519", "682015", "678002", "673633", "682507", "695013", "670701", "673517", "688533", "673506", "682004", "676104", "686564", "688005", "688529", "678598", "686665", "680581", "685616", "686584", "688505", "688582", "686533", "678510", "685511", "679515", "680003", "695542", "673601", "686041", "686632", "670661", "673573", "673307", "678006", "670662", "673301", "686004", "695017", "682029", "686121", "685590", "691551", "678591", "685604", "685586", "688537", "680509", "679328", "680511", "670675", "682044", "682026", "689109", "671326", "686501", "689692", "683589", "686562", "695507", "676123", "680542", "690526", "680618", "689644", "689591", "680026", "670571", "688527", "676509", "691312", "691560", "678104", "670674", "678631", "690515", "679303", "679573", "678503", "679306", "695012", "689107", "670673", "680520", "686587", "695521", "686664", "680734", "682038", "680687", "676122", "695016", "680583", "680502", "695027", "680614", "690540", "678574", "670731", "690528", "678009", "695573", "691308", "686692", "682033", "690538", "689677", "670567", "679330", "691006", "689666", "689546", "673315", "678632", "673310", "670604", "673575", "688003", "686635", "686555", "682557", "682551", "682559", "682555", "682553", "682552", "682556", "682554", "682558", "533296", "607403", "509133", "605006", "609601", "609607", "607402", "609603", "605011", "605001", "605013", "609606", "605007", "605008", "609609", "605002", "605102", "605003", "609605", "605104", "605009", "605010", "605004", "605110", "605005", "605502", "609604", "609602", "605014", "629202", "639101", "623566", "638010", "642114", "625018", "627413", "621003", "628701", "625122", "635304", "627110", "628615", "625514", "629175", "605402", "609003", "625536", "621715", "632316", "632204", "609302", "603001", "622401", "632007", "643239", "610203", "637001", "603108", "638706", "625015", "612902", "606706", "603201", "627759", "605601", "628218", "605501", "631501", "627811", "628502", "606802", "606202", "605401", "624215", "636103", "624620", "638116", "637405", "609109", "609306", "642206", "630713", "624003", "614906", "600008", "630610", "628809", "629151", "623703", "626116", "613102", "641402", "610003", "606201", "603406", "627452", "606107", "609703", "603309", "632603", "624701", "641605", "638183", "638112", "609106", "603107", "600045", "631701", "620101", "605752", "627857", "605758", "609803", "604304", "632013", "627602", "606801", "635001", "614210", "632312", "638055", "628105", "628702", "631303", "623515", "627809", "627760", "608704", "604503", "621102", "624210", "625705", "605108", "622304", "625020", "637402", "600078", "643105", "604410", "621009", "638102", "621206", "614203", "600002", "638051", "623401", "630551", "638301", "642104", "600044", "642133", "606906", "622101", "638106", "632518", "601101", "625540", "610107", "628801", "638751", "627859", "626122", "626129", "627761", "637021", "623524", "636402", "635305", "606207", "600004", "614626", "606003", "636458", "636301", "638459", "636902", "643219", "625582", "638462", "608002", "627357", "624220", "623315", "632520", "600127", "632502", "643243", "624614", "637003", "609404", "600042", "638312", "625517", "638505", "614302", "636701", "636452", "635751", "613705", "626002", "609117", "624216", "639119", "600122", "623608", "600039", "641201", "638457", "600066", "642127", "643238", "624802", "628301", "641664", "629852", "625579", "626113", "613105", "632510", "604303", "627201", "623707", "643103", "630302", "638752", "642006", "641666", "624402", "624801", "643207", "628229", "628904", "638056", "625528", "636010", "627818", "600101", "606751", "628212", "605756", "624610", "621014", "609313", "621701", "627415", "614624", "614204", "600047", "627005", "641653", "635815", "620003", "641606", "639136", "641043", "623520", "625701", "641601", "637503", "627807", "628004", "622502", "600107", "631052", "632507", "606754", "614102", "642106", "637303", "605101", "600074", "603305", "606401", "620010", "607107", "630321", "625234", "627652", "632519", "602003", "614402", "604210", "636115", "643101", "614401", "605751", "627401", "627418", "638057", "614015", "637204", "626127", "638108", "626136", "608703", "627753", "603204", "609301", "627012", "621652", "628905", "612603", "636404", "626109", "626104", "627603", "606710", "641006", "631207", "614614", "625520", "621216", "604151", "612102", "632004", "624707", "625537", "629804", "641113", "622204", "609113", "635301", "630313", "639117", "641302", "614807", "636015", "614403", "630104", "636813", "606104", "637304", "623533", "625103", "642129", "600117", "607105", "612202", "630210", "636204", "607108", "632531", "630212", "642007", "624618", "627006", "636109", "621001", "609704", "631003", "643231", "604202", "623711", "603211", "622002", "636808", "621305", "621103", "636455", "636307", "626005", "621313", "632317", "613601", "613204", "621209", "624403", "600041", "602025", "642111", "614620", "609203", "629001", "631202", "625221", "604152", "636352", "641004", "627004", "636454", "635653", "601103", "641114", "625706", "637212", "642107", "630005", "626125", "627359", "614706", "623120", "609104", "624621", "625521", "625512", "623518", "606203", "628903", "607803", "620005", "614625", "621204", "636014", "625005", "612201", "621109", "600073", "632057", "605036", "614809", "630208", "627117", "636406", "606707", "629703", "632008", "600081", "614801", "614103", "642201", "614803", "641005", "630108", "621314", "625525", "629164", "609405", "607202", "627356", "621718", "643228", "635116", "608201", "631212", "621804", "637017", "641603", "630211", "605759", "635117", "635112", "611103", "622507", "627713", "626607", "641049", "614020", "636013", "626126", "635852", "605766", "603202", "627657", "621704", "600021", "609403", "638104", "614715", "630003", "625704", "600126", "641035", "606908", "630206", "636001", "636403", "627152", "635302", "600082", "602108", "629160", "641037", "606303", "637103", "632602", "608001", "627109", "605651", "632002", "624616", "631502", "610202", "636122", "611105", "603301", "609312", "614101", "636303", "632103", "641026", "600036", "627654", "627114", "629809", "625014", "623532", "614001", "600025", "624301", "636351", "628908", "643220", "642203", "606755", "621006", "609103", "614716", "641046", "632504", "612501", "636906", "601206", "606753", "629180", "614404", "626123", "628612", "600031", "621008", "628952", "628102", "630502", "627802", "630410", "627354", "641604", "627358", "621730", "607302", "624307", "639001", "623402", "609101", "628101", "641003", "624306", "643237", "631301", "641009", "629167", "609309", "627119", "631101", "603303", "621007", "604407", "627856", "637102", "609807", "630303", "625562", "641665", "627104", "631208", "628503", "631002", "613005", "643213", "609102", "637207", "620001", "600080", "604203", "631005", "636302", "632115", "625105", "612106", "604208", "639002", "628613", "630312", "607301", "604501", "642105", "622203", "630612", "621214", "600104", "641669", "611001", "606204", "606709", "636305", "622411", "613502", "624308", "613201", "643241", "621205", "606905", "641023", "626134", "606701", "614618", "636401", "612804", "629165", "638005", "635701", "636201", "625008", "623534", "625534", "636116", "632601", "600106", "601102", "643204", "627107", "629157", "609116", "614901", "636004", "628653", "629810", "623603", "631702", "606402", "632203", "601203", "627755", "632314", "635812", "629004", "636016", "600098", "623601", "635103", "622301", "630309", "632209", "609501", "630205", "628151", "636142", "609118", "604001", "603302", "636111", "627351", "639006", "628552", "603308", "600061", "636457", "629704", "635110", "639103", "625023", "643202", "637014", "614623", "603310", "636101", "621805", "605203", "631152", "604402", "614810", "632105", "620025", "622303", "609402", "603110", "627764", "627862", "631151", "635113", "622409", "639005", "604408", "603106", "627719", "623521", "620018", "636011", "621115", "628219", "621012", "624103", "625529", "628716", "638003", "636306", "642003", "643240", "614712", "631213", "635307", "600128", "625019", "628619", "600116", "628902", "642128", "639105", "628251", "604102", "627428", "620011", "600040", "623406", "641033", "614621", "627751", "614207", "636904", "621111", "632405", "621801", "638451", "613205", "623605", "629102", "621702", "605801", "611110", "635652", "627151", "632503", "600093", "629251", "614019", "630314", "637101", "636501", "642002", "628802", "623537", "627805", "604205", "639113", "614017", "638052", "631051", "628201", "602002", "641042", "638461", "627106", "627756", "613101", "641663", "641107", "621010", "637412", "607201", "636009", "625021", "641024", "626137", "624303", "637406", "614738", "600018", "632202", "629803", "600043", "625207", "638103", "636108", "613010", "635207", "623407", "613602", "630566", "632508", "637403", "629701", "609805", "628901", "627813", "641105", "631204", "635602", "609205", "606807", "635702", "636141", "612002", "614617", "625702", "624709", "623514", "628618", "611111", "607112", "630105", "621713", "641018", "608401", "628152", "600068", "630555", "609804", "641301", "642125", "639118", "603314", "600119", "636012", "632401", "608502", "614619", "621802", "600056", "637104", "609310", "625703", "643007", "638454", "638501", "625006", "630301", "625513", "627118", "608304", "632104", "627453", "607401", "624219", "626105", "624304", "631304", "600071", "627425", "636456", "600102", "621310", "626108", "636804", "631006", "630306", "628721", "600084", "625708", "632106", "625001", "641687", "613007", "643002", "612203", "627416", "609806", "632604", "628703", "626189", "636451", "622001", "607103", "642109", "638703", "600124", "621307", "607807", "630602", "600072", "608306", "625602", "600094", "623529", "642004", "641662", "643214", "638001", "638182", "621708", "612302", "630553", "620024", "600054", "603304", "636121", "606704", "636002", "629602", "636602", "641041", "642130", "627426", "613003", "622501", "636812", "627003", "627102", "638313", "630562", "620017", "609702", "627806", "621112", "626118", "628216", "638506", "628304", "603101", "638452", "614629", "629601", "641019", "600035", "638453", "631552", "627112", "625516", "604401", "603307", "612303", "600055", "608901", "621219", "632102", "629162", "636138", "614904", "627120", "635710", "643205", "641032", "636809", "638812", "631601", "600113", "635108", "643206", "606105", "628614", "636304", "621002", "626128", "624619", "626106", "628722", "627427", "638316", "610004", "632001", "621601", "629172", "606103", "600088", "623527", "622102", "641015", "612503", "635651", "638181", "635808", "630202", "621133", "621315", "635126", "625603", "614701", "641007", "603203", "637409", "630710", "612204", "627424", "632107", "635106", "631551", "609503", "627105", "613002", "635123", "641108", "627423", "606115", "612801", "622403", "623525", "603403", "643217", "623806", "628718", "604201", "625535", "632512", "623526", "627001", "630106", "630411", "635703", "636203", "643203", "600026", "612605", "643242", "627804", "607102", "636704", "637020", "613301", "614208", "641671", "641658", "625556", "628001", "638455", "612001", "612901", "642113", "637215", "614806", "621118", "606703", "630405", "637202", "600062", "632506", "611003", "638502", "600030", "638504", "626139", "625523", "630203", "600069", "630002", "641036", "621208", "639207", "628252", "625022", "641031", "642112", "630305", "627502", "603311", "604601", "622402", "632059", "632301", "639112", "635119", "636803", "638456", "613202", "606601", "638002", "641103", "613004", "638115", "627951", "631102", "607101", "624002", "613701", "620021", "614702", "624706", "614717", "625601", "637107", "638101", "608305", "632517", "609305", "635655", "614708", "642123", "627101", "611102", "626124", "610206", "628753", "609112", "623502", "610101", "627758", "600085", "629166", "600046", "600015", "641670", "605803", "628213", "642101", "604154", "605754", "641202", "613703", "629152", "604204", "612803", "629201", "625108", "620014", "638151", "600103", "606001", "607801", "600022", "604505", "600099", "609107", "630561", "627011", "623308", "641109", "609114", "621105", "625503", "636811", "632513", "629502", "612610", "610103", "638054", "609502", "607203", "612301", "600012", "636007", "641112", "632319", "620006", "607006", "612701", "600016", "635809", "604405", "629802", "642126", "610109", "637404", "627010", "641045", "621308", "622504", "614713", "603405", "632516", "632315", "602117", "630609", "643218", "642102", "638315", "624703", "600009", "614613", "605702", "620002", "614205", "641039", "641401", "632318", "636118", "610201", "604305", "613008", "620016", "603401", "606305", "624613", "603112", "627858", "614905", "629003", "614808", "638476", "626204", "606304", "638402", "636140", "637501", "600053", "636112", "625102", "641667", "639114", "629156", "606209", "630501", "638111", "609608", "623512", "603102", "629301", "629163", "602021", "631604", "600064", "639007", "600014", "611112", "641013", "621004", "600019", "642005", "632514", "630207", "627808", "613104", "635115", "621301", "628616", "621722", "614704", "636503", "641110", "600095", "622103", "626203", "600123", "621653", "627852", "621706", "627103", "621202", "608303", "625604", "636309", "609808", "641002", "624710", "627202", "628008", "625402", "600092", "630101", "636308", "624101", "621213", "641001", "639107", "638311", "626117", "628303", "609304", "614303", "638702", "603127", "626149", "632014", "638004", "602106", "636705", "626142", "604504", "622503", "627116", "626138", "625004", "637504", "620022", "623115", "632114", "609307", "603313", "637201", "635806", "628209", "606102", "641047", "635810", "643209", "636114", "632012", "621707", "606111", "621710", "623501", "643003", "602023", "623705", "613503", "608601", "600049", "621215", "622201", "637210", "610105", "600003", "622209", "610106", "635804", "607209", "605757", "600118", "626115", "642108", "626001", "636202", "628402", "608602", "600089", "641602", "627501", "621712", "611002", "605755", "625012", "621113", "628203", "630559", "635805", "635114", "643212", "638006", "600083", "600087", "626131", "600028", "631606", "623528", "627115", "643005", "637019", "641050", "621651", "610205", "620012", "627860", "623404", "642122", "621207", "620023", "600076", "603306", "635854", "605701", "636806", "609108", "604502", "643221", "621803", "621312", "625218", "606705", "613001", "614902", "604207", "637301", "613702", "627851", "621316", "639003", "623604", "600058", "641104", "631553", "612601", "614018", "641022", "641008", "611109", "606611", "627414", "628752", "630204", "641652", "607303", "604153", "627131", "613303", "635204", "620026", "605201", "622004", "630709", "636113", "627853", "625106", "636601", "609504", "631206", "641028", "629161", "626140", "638110", "627111", "632313", "628907", "637018", "626111", "641011", "610001", "642202", "631211", "622407", "635654", "611101", "630557", "608501", "613103", "603210", "625531", "606811", "605602", "639109", "614711", "629173", "628007", "635111", "605111", "624601", "629851", "641017", "609401", "606205", "621104", "628601", "630552", "641034", "612604", "626133", "614616", "638656", "635802", "629174", "614805", "637408", "612903", "609204", "624705", "622005", "601202", "626004", "639206", "620102", "636805", "621302", "639008", "642103", "636107", "635303", "620008", "627812", "638008", "606208", "621711", "627412", "600033", "636117", "630107", "621311", "600005", "625518", "606702", "614802", "642117", "641012", "621217", "632009", "636807", "600020", "643102", "638460", "605107", "613402", "600051", "626202", "629002", "621110", "636105", "639120", "624201", "627009", "604306", "636139", "604307", "631210", "621011", "627861", "625532", "600013", "636003", "625707", "629158", "627754", "612402", "620020", "637214", "625526", "642134", "638153", "605802", "628751", "632113", "623135", "602105", "608702", "606808", "635853", "628215", "608801", "643225", "628204", "625524", "600077", "635901", "642205", "610104", "632201", "621851", "627352", "614628", "613006", "600007", "609801", "635206", "626612", "624617", "629801", "625605", "630554", "606603", "628906", "639115", "641048", "641655", "609115", "635121", "600017", "630611", "636502", "638009", "614615", "636453", "632058", "632055", "630307", "626188", "629204", "609001", "626112", "643224", "638314", "607205", "628704", "637410", "628103", "625527", "635801", "629403", "605302", "614301", "604301", "606901", "620013", "613009", "607805", "612502", "631004", "600129", "638012", "612703", "623504", "637208", "637206", "622505", "621714", "612103", "638660", "631302", "641407", "622202", "627422", "628656", "635752", "636102", "641020", "625104", "614602", "629402", "600125", "628720", "637002", "627659", "600100", "632311", "607308", "628851", "636905", "601301", "613504", "614201", "600115", "600023", "638661", "621306", "605106", "600075", "630311", "605652", "632521", "637407", "641021", "635811", "638458", "635109", "627108", "627803", "636030", "600032", "622003", "613704", "613401", "606904", "609701", "614601", "607004", "608701", "638673", "621117", "609811", "639116", "609314", "624711", "635118", "638503", "627421", "628006", "632501", "621116", "625522", "638105", "614710", "603002", "638401", "602026", "627417", "600067", "614016", "606907", "643201", "600006", "604409", "627007", "623517", "628401", "643216", "622515", "628622", "632403", "625201", "624208", "603103", "613203", "643211", "600060", "627008", "600037", "639202", "635105", "623523", "637203", "600091", "636104", "606903", "629176", "641014", "627127", "635201", "632515", "632406", "625011", "636810", "612904", "623530", "628005", "612104", "624302", "635122", "624704", "620004", "609105", "629501", "612401", "629178", "639102", "639205", "639108", "628621", "641659", "600059", "631402", "630702", "628003", "622506", "604406", "611106", "621108", "625003", "600052", "641101", "639111", "630201", "608301", "608102", "635203", "627002", "623701", "641305", "606804", "625009", "642110", "626101", "631209", "641654", "600096", "625007", "614206", "623704", "600011", "614903", "628208", "637013", "600048", "638011", "625301", "623536", "636110", "626114", "614714", "613403", "609201", "632326", "630102", "603402", "600110", "612802", "600065", "606106", "637211", "635814", "625533", "629155", "626121", "626102", "631605", "637401", "632010", "625109", "624212", "609810", "635601", "632006", "600001", "606803", "610102", "614622", "623503", "624712", "604404", "623522", "641029", "625107", "606708", "600057", "630558", "621005", "628211", "628623", "628207", "638107", "621220", "624211", "600130", "612602", "639203", "627953", "604101", "638109", "635120", "631561", "629159", "600112", "600086", "637411", "628104", "600070", "625214", "643253", "607204", "624612", "632404", "637302", "621218", "601201", "643236", "606301", "637105", "607001", "628617", "606108", "629154", "635813", "637015", "609202", "623516", "626132", "628714", "642204", "606302", "623708", "636903", "629153", "623712", "626135", "637502", "638657", "626201", "602001", "642132", "627451", "635202", "621719", "626110", "635102", "635205", "625101", "606604", "627604", "643233", "631203", "622302", "623538", "621703", "629901", "606806", "625002", "631205", "623409", "623706", "626141", "606206", "628210", "600034", "614211", "635851", "638672", "625203", "638701", "625501", "635306", "626103", "627355", "642001", "641697", "625519", "625017", "627814", "642154", "621106", "639004", "638154", "641027", "614014", "607104", "628205", "603209", "641111", "603319", "628501", "625205", "639201", "641038", "635803", "605105", "621107", "643215", "627133", "635754", "631603", "624005", "621212", "604403", "628620", "624702", "628217", "614630", "614202", "624401", "628202", "611108", "627601", "609303", "625515", "637213", "628302", "602024", "623531", "614723", "622422", "635124", "604302", "632011", "614707", "621717", "607106", "609110", "641030", "629170", "614705", "624206", "613501", "626003", "607804", "631001", "629179", "621709", "623519", "628206", "643006", "637505", "627854", "606902", "635807", "641607", "609111", "622104", "636106", "606805", "629101", "636005", "635101", "643223", "621210", "631201", "629177", "614804", "621114", "627651", "636354", "638007", "629302", "600120", "630103", "626130", "638053", "621806", "620007", "621101", "621705", "627353", "627113", "607005", "606110", "630556", "627855", "605103", "624204", "624622", "624202", "642120", "621729", "625530", "623403", "603104", "627757", "600038", "600050", "612702", "635104", "614013", "612101", "628712", "621716", "601204", "629168", "600010", "603105", "612504", "614703", "600090", "625110", "612105", "603109", "629193", "637205", "629203", "620009", "629171", "635002", "604206", "622404", "641044", "607003", "614612", "636008", "643004", "629702", "638152", "635107", "626107", "639110", "632101", "606213", "624004", "600097", "623513", "609308", "641062", "610207", "606752", "636006", "622412", "605301", "632511", "632505", "601205", "625016", "630606", "643226", "629252", "607109", "621203", "641010", "629401", "626205", "603003", "642207", "627420", "620019", "620015", "629169", "600024", "641016", "626119", "624708", "630001", "610204", "621211", "641025", "611104", "639104", "607002", "603111", "643001", "605109", "609802", "603312", "628002", "624001", "609311", "632509", "607802", "641668", "624615", "636119", "605202", "600063", "630408", "637209", "608302", "505481", "507133", "509352", "508126", "505531", "500063", "508201", "507125", "501101", "508211", "500002", "505415", "508376", "509206", "504301", "509235", "507159", "506167", "506134", "505503", "505122", "500089", "507122", "503174", "508223", "505501", "505527", "503123", "509411", "502102", "502001", "502125", "508205", "501144", "503185", "500093", "506169", "506356", "507120", "504205", "506252", "508213", "502290", "502312", "508377", "506352", "503001", "505403", "504292", "509216", "503309", "500061", "509353", "508280", "500015", "502294", "504306", "506166", "503112", "502381", "500037", "508114", "505528", "502301", "504308", "509130", "504220", "504294", "500042", "507303", "509201", "504293", "506164", "507121", "500035", "509103", "500064", "501102", "505208", "508373", "508202", "505502", "506315", "507140", "508256", "505460", "509410", "507135", "500072", "501512", "500049", "500007", "500091", "506007", "506112", "500040", "507316", "505470", "500018", "500082", "504208", "501501", "503002", "504110", "505186", "507170", "505467", "500004", "500055", "505326", "509132", "500013", "502251", "502336", "503224", "505474", "506302", "505101", "505210", "507117", "506301", "504312", "504311", "503101", "502280", "505212", "500039", "509217", "506132", "501504", "501301", "503213", "501508", "505152", "507101", "509207", "504106", "500133", "509406", "509349", "507103", "505187", "500020", "500031", "500092", "504214", "506165", "501143", "504346", "503235", "502273", "509326", "507166", "506371", "507158", "509375", "504310", "500010", "507201", "501218", "509357", "500070", "505505", "507137", "502108", "502302", "504303", "504203", "503120", "505174", "509152", "505504", "500006", "507111", "503165", "500058", "509302", "509215", "506224", "506145", "509408", "506175", "502307", "505524", "506015", "505402", "509203", "509126", "505307", "502249", "504201", "504215", "505450", "506355", "501401", "500036", "500014", "503102", "506170", "505184", "508204", "500003", "502300", "505471", "509336", "506319", "503145", "506342", "504219", "505153", "500079", "508254", "504101", "507304", "503125", "504103", "503114", "509120", "505405", "504001", "506004", "507123", "501502", "505327", "500060", "500096", "506367", "503302", "505514", "500090", "507305", "500008", "502110", "508217", "505469", "508210", "508116", "502032", "505404", "502130", "502277", "503124", "502221", "505214", "500044", "500084", "505209", "500077", "502329", "508117", "500078", "506001", "506008", "506006", "505303", "507138", "508221", "509311", "504231", "509110", "507134", "502291", "508004", "504107", "500097", "502331", "509339", "508278", "502287", "506168", "507136", "502324", "503207", "505330", "505475", "507169", "502372", "507001", "500046", "508208", "509335", "506331", "502205", "505425", "500094", "505497", "502286", "506122", "502107", "505401", "502114", "503322", "500026", "501505", "508206", "506314", "502295", "502210", "500100", "507301", "508374", "502246", "505476", "508277", "508112", "509002", "505416", "505498", "508245", "500101", "506201", "509219", "501202", "500076", "503164", "505301", "500045", "508113", "505480", "500073", "500069", "503212", "501111", "500088", "500057", "500048", "502271", "503225", "509385", "504216", "509102", "502316", "500034", "505490", "506223", "505453", "509324", "504209", "509382", "503110", "504207", "502318", "504273", "509001", "509371", "502314", "503306", "508286", "505451", "503246", "509412", "502117", "506221", "503219", "508233", "507154", "506347", "503308", "507157", "506151", "508234", "509358", "500074", "509301", "509381", "503217", "500068", "505466", "504309", "502220", "505452", "509337", "506348", "509351", "509338", "504295", "509204", "509131", "505530", "508258", "509128", "506318", "505325", "506365", "505445", "508247", "504102", "504251", "505529", "509321", "509153", "505305", "507115", "504109", "507168", "509360", "504202", "505462", "505473", "500051", "507211", "500047", "504105", "500022", "508101", "509135", "506011", "505454", "502310", "505188", "502305", "509105", "502296", "505306", "507210", "506244", "506370", "505172", "506142", "500080", "507167", "502325", "506003", "501511", "502248", "500012", "503310", "507183", "505468", "504002", "501510", "503144", "509376", "507208", "503223", "503307", "500052", "503188", "502345", "503305", "505532", "508285", "506329", "507124", "505102", "500059", "503187", "500032", "500067", "501158", "500054", "506313", "500030", "500062", "501359", "509125", "502269", "500017", "502101", "500024", "504307", "508212", "508266", "505525", "508115", "503003", "509208", "509210", "502371", "508105", "505215", "506303", "508111", "501121", "507116", "501142", "503301", "502103", "504218", "506143", "500038", "500065", "509327", "500098", "507302", "507002", "503122", "507204", "506324", "509209", "500005", "506005", "502115", "500001", "507163", "502321", "508001", "507118", "504296", "501503", "502257", "504302", "505304", "503108", "500086", "509104", "509334", "507003", "502113", "508244", "500041", "500087", "502281", "500095", "506144", "506104", "503111", "502270", "502256", "507160", "506381", "500009", "500085", "503230", "508224", "507161", "501141", "508252", "507209", "505185", "506317", "502228", "509401", "500027", "502278", "503218", "504299", "506009", "503321", "507182", "505129", "506172", "509106", "507128", "508284", "506163", "509127", "500081", "506344", "508250", "507113", "508257", "500025", "506013", "500028", "509129", "500029", "509380", "500033", "500083", "505331", "503206", "502375", "502311", "506369", "505162", "502109", "503186", "505455", "506316", "507306", "503175", "509320", "502285", "503202", "506310", "508218", "507203", "501509", "502303", "508255", "507114", "506343", "508238", "506222", "503180", "502334", "509350", "502335", "508002", "500019", "502255", "506349", "509407", "508248", "508253", "509325", "506332", "500075", "503311", "509409", "509340", "509205", "505302", "509202", "501106", "508243", "508246", "509228", "500043", "506345", "508207", "502319", "500023", "500066", "502267", "506391", "504313", "506368", "502293", "507202", "504304", "506105", "505526", "501506", "506135", "506330", "500011", "506102", "506002", "500050", "506366", "502247", "502306", "504206", "508355", "500016", "504297", "507119", "507165", "506101", "504323", "507164", "500056", "503201", "502276", "501203", "504204", "507318", "502313", "504104", "505001", "504272", "505211", "500053", "503245", "505472", "508279", "507129", "502279", "560117"], "exlcude_cities": []}, "groups_name": "Abhilash - South"},{"id": 238, "group_data": {"cities": [], "states": [], "pincode": ["791119", "791112", "791111", "792102", "790003", "790001", "792111", "790116", "792123", "792120", "790002", "791001", "791104", "790104", "791103", "791125", "791102", "792121", "791101", "790102", "792104", "791113", "791003", "792056", "792101", "790114", "791123", "792129", "792122", "791118", "790103", "791002", "791120", "791121", "790101", "791110", "792055", "790105", "791122", "791109", "792110", "792130", "790106", "792105", "792001", "791105", "792131", "792103", "781338", "783375", "784154", "783325", "786181", "782122", "784125", "783121", "783133", "782461", "782448", "788117", "788724", "784167", "788014", "783393", "784172", "781171", "783336", "787056", "784161", "784506", "784001", "782482", "788720", "784180", "788010", "785675", "782462", "781305", "786601", "788119", "781335", "784114", "781370", "784146", "785101", "785674", "788710", "784025", "788726", "788737", "788009", "786171", "786622", "788098", "788104", "788150", "785625", "782105", "782485", "788725", "787110", "781124", "786158", "783360", "788099", "781125", "785108", "788156", "788006", "784174", "783332", "784171", "781024", "788003", "781138", "781376", "782136", "784104", "788931", "783330", "781382", "784505", "782140", "783347", "788108", "781317", "782460", "782425", "788701", "788163", "784028", "785689", "784147", "783383", "781318", "786192", "785697", "788114", "788713", "786147", "781301", "783339", "786155", "786692", "781302", "785102", "781023", "788127", "784149", "783388", "787060", "786621", "783361", "782121", "785001", "781326", "784145", "785688", "788115", "784521", "786613", "788728", "784148", "784190", "786150", "786189", "781377", "782126", "783394", "782428", "784176", "785105", "785111", "781351", "781310", "788804", "785006", "788736", "781328", "786173", "781129", "783376", "781101", "783348", "782429", "786154", "781007", "782441", "782447", "784010", "788112", "783324", "782490", "781137", "781120", "785601", "781134", "781135", "781325", "784526", "788719", "787053", "781025", "783134", "786125", "785013", "788101", "781369", "785106", "784113", "788831", "786610", "781352", "788818", "786179", "785104", "781035", "781008", "785693", "787026", "784164", "781026", "788723", "782470", "785661", "788820", "783392", "781015", "782003", "783350", "782138", "784527", "782141", "781150", "781031", "786184", "785673", "785609", "788106", "788118", "788001", "785630", "788121", "781319", "782135", "785004", "788805", "786159", "783345", "784177", "784508", "782481", "787034", "781003", "781365", "783349", "785667", "788151", "783385", "782101", "788113", "787001", "781343", "782435", "782001", "781103", "788102", "784144", "781346", "784105", "783122", "782480", "784101", "786623", "781381", "785702", "788103", "781011", "788155", "786614", "782106", "785635", "785701", "785686", "781349", "781004", "783126", "788817", "784525", "784160", "788803", "785698", "783331", "781016", "783389", "781327", "781367", "781375", "783120", "784169", "788162", "785662", "783123", "781355", "781005", "783132", "788722", "781308", "784513", "787058", "788111", "786146", "784182", "783391", "784163", "782450", "781010", "788007", "788161", "785611", "786190", "781337", "788116", "781122", "785622", "785616", "781123", "788109", "788781", "784509", "784165", "785665", "781303", "786153", "783381", "785669", "784103", "788830", "786004", "782411", "784150", "781371", "785110", "785007", "786145", "782402", "785699", "788711", "781309", "784502", "788013", "781020", "781333", "781329", "781131", "786103", "781344", "788126", "785640", "781350", "783346", "787031", "785010", "781334", "785682", "781314", "784115", "783124", "784026", "781038", "782446", "783135", "784504", "784501", "781364", "788031", "783370", "784117", "783371", "782426", "781306", "784110", "781009", "785602", "788107", "785614", "785704", "784027", "782143", "783380", "782120", "782144", "782401", "782439", "784510", "784524", "785636", "781321", "781374", "784522", "788002", "786102", "787023", "785691", "781368", "785692", "784170", "782102", "786126", "785015", "784168", "787052", "783354", "781012", "782104", "784102", "788709", "785107", "787055", "782127", "788727", "781373", "785705", "781128", "786174", "785676", "781316", "788168", "784507", "785690", "781027", "781029", "782410", "781340", "783384", "786612", "788733", "781030", "781126", "781028", "785696", "783131", "787033", "785684", "786002", "788806", "785612", "783337", "782139", "781360", "782137", "785615", "781312", "785014", "781018", "782486", "785619", "786611", "788166", "786001", "788734", "781104", "786007", "783369", "788712", "786010", "781136", "786183", "788832", "785631", "783323", "788026", "781354", "782125", "781013", "784173", "787057", "781040", "782103", "782002", "782442", "781372", "786188", "784178", "785613", "787059", "786182", "786152", "781307", "785672", "786602", "784116", "785666", "785700", "782445", "781141", "786003", "781022", "788160", "782412", "785664", "782413", "788816", "788110", "787061", "786101", "785687", "786005", "781304", "781133", "781014", "781330", "786187", "781019", "781339", "781102", "783101", "783333", "783301", "781366", "785683", "781347", "784175", "785626", "783129", "784112", "786151", "782123", "788165", "784523", "781039", "785634", "781380", "782427", "787051", "788123", "783373", "788815", "786156", "784528", "783382", "787032", "783372", "788030", "784153", "785603", "785632", "788801", "782142", "786012", "787054", "781021", "783128", "781348", "781006", "784166", "781036", "781127", "788819", "781353", "782124", "787035", "785009", "783334", "785112", "781341", "788802", "786006", "781001", "786160", "785685", "788164", "788735", "782403", "782440", "785633", "783335", "785670", "781378", "784179", "784529", "788025", "785008", "788005", "784514", "781315", "788004", "785681", "785671", "783127", "783130", "788011", "781313", "788015", "781017", "784111", "788120", "783390", "786157", "786191", "785663", "783125", "784184", "786170", "781132", "781311", "786008", "784189", "788152", "781034", "785618", "781032", "785703", "781121", "785610", "785680", "785621", "781037", "282006", "283105", "282005", "282002", "283101", "283114", "283113", "282008", "282007", "283124", "283126", "283201", "283122", "283111", "283123", "283121", "283125", "283104", "282001", "283119", "283202", "282003", "282010", "110023", "110034", "110058", "110030", "110015", "110049", "110091", "110002", "110009", "110033", "110004", "110028", "110059", "110087", "110051", "110080", "110048", "110078", "110046", "110062", "110031", "110089", "110088", "110094", "110072", "110090", "110019", "110065", "110086", "110075", "110025", "110071", "110066", "110017", "110070", "110007", "110038", "110060", "110083", "110068", "110069", "110010", "110054", "110073", "110045", "110082", "110011", "110055", "110018", "110057", "110014", "100001", "110037", "110016", "110097", "110085", "110042", "110036", "110095", "110052", "110067", "110039", "110056", "110013", "110093", "110040", "110032", "110026", "110005", "110077", "110092", "110043", "110029", "110053", "110076", "110041", "110035", "110047", "110021", "110003", "110020", "110061", "110024", "110064", "110008", "110063", "110027", "110022", "110074", "110081", "110096", "110012", "110001", "110044", "110084", "110006", "282009", "283112", "283115", "283110", "283102", "282004", "131409", "131305", "202141", "202150", "202136", "202139", "131101", "122051", "202145", "202123", "202282", "202142", "202135", "202121", "202126", "202125", "204215", "122414", "202138", "202128", "202146", "202134", "202127", "202140", "122503", "122502", "204213", "202165", "202281", "202002", "131039", "121105", "202129", "131304", "122508", "202001", "202131", "202155", "131023", "202132", "202280", "202122", "202137", "202143", "202133", "202170", "122011", "202130", "202124", "122018", "122008", "121003", "121009", "122010", "122015", "122104", "121010", "131306", "131408", "121002", "131102", "131103", "131403", "131027", "122101", "122103", "122504", "122016", "122081", "121008", "131024", "131302", "122505", "101213", "131022", "122001", "122107", "122009", "132011", "122022", "122017", "121103", "121106", "122102", "121006", "121102", "121013", "122108", "122004", "122005", "121101", "121004", "131001", "121107", "131301", "123011", "122105", "122050", "122052", "121007", "131029", "122506", "122003", "250621", "121005", "122202", "122007", "121001", "250345", "250611", "250619", "131028", "250606", "250623", "250601", "250625", "250617", "250101", "250615", "250622", "250620", "250626", "122006", "131021", "250609", "131402", "122413", "122002", "121012", "795010", "795112", "795004", "795146", "795147", "795145", "795135", "795136", "795125", "795103", "795104", "795126", "795101", "795133", "795134", "795139", "795117", "795001", "795130", "795116", "795118", "795141", "795148", "795106", "795122", "795150", "795132", "795008", "795007", "795129", "795006", "795102", "795114", "795142", "795143", "795009", "795005", "795140", "795124", "795127", "795015", "795002", "795128", "795131", "795115", "795113", "795149", "795107", "795144", "795003", "795138", "795159", "793006", "793103", "793002", "793102", "793108", "793111", "793018", "793014", "793106", "793107", "794112", "794101", "793210", "794108", "793008", "793009", "793113", "793150", "793112", "793022", "794115", "794105", "793005", "793151", "794005", "793120", "793017", "793004", "793110", "794103", "793104", "793012", "794106", "794109", "793015", "793019", "794110", "794001", "793010", "793123", "793105", "793001", "793122", "794111", "793114", "793121", "793115", "793119", "794114", "793109", "794102", "793016", "793160", "794104", "794002", "793021", "793011", "793200", "793101", "793003", "794107", "793007", "796186", "796181", "796081", "796751", "796101", "796111", "796701", "796014", "796320", "796321", "796007", "796891", "796290", "796710", "796410", "796004", "796441", "796082", "796012", "796901", "796581", "796310", "796691", "796370", "796001", "796190", "796261", "796501", "796421", "796184", "796161", "796091", "796471", "796431", "796571", "796730", "796017", "796770", "796005", "796009", "796008", "796070", "798612", "798611", "797120", "798616", "797103", "797003", "797113", "797121", "798623", "797106", "797101", "797109", "797002", "797112", "797001", "798615", "797102", "798613", "798604", "798601", "798618", "798625", "797104", "798620", "798619", "797005", "798622", "798603", "798602", "798627", "797111", "797118", "797099", "797107", "797114", "797004", "797105", "797110", "797116", "798617", "797100", "798621", "797117", "797108", "798614", "798626", "797115", "782128", "794003", "737113", "737116", "737135", "737139", "737138", "737106", "737118", "737101", "737133", "737128", "737136", "737126", "737117", "737102", "737131", "737134", "737111", "737132", "737119", "737103", "737107", "737120", "737121", "799269", "799278", "799211", "799251", "799261", "799002", "799201", "799270", "799010", "799145", "799045", "799253", "799204", "799114", "799001", "799279", "799012", "799150", "799281", "799008", "799275", "799120", "799256", "799284", "799254", "799290", "799153", "799289", "799288", "799143", "799102", "799022", "799205", "799009", "799006", "799262", "799210", "799046", "799003", "799277", "799007", "799013", "799115", "799263", "799035", "799125", "799104", "799250", "799157", "799155", "799015", "799005", "799203", "799271", "799144", "799141", "799266", "799202", "799287", "799014", "799280", "799260", "799101", "799264", "799207", "799131", "799273", "799142", "799285", "799004", "799130", "799286", "799212", "799105", "799156", "799103", "234001", "250222", "212653", "201102", "201003", "250205", "212622", "250103", "201012", "212658", "250402", "201201", "203392", "203403", "203412", "203201", "203395", "203150", "203141", "203409", "203155", "203398", "203399", "203408", "203402", "203205", "203129", "203132", "203396", "203390", "203393", "203206", "203401", "203001", "203397", "203131", "203411", "203389", "203405", "203002", "203391", "203407", "203394", "212620", "212659", "212664", "207401", "207302", "207122", "207402", "207403", "207244", "207248", "207002", "207241", "207123", "207242", "207001", "250004", "207124", "207301", "207249", "207250", "212663", "207003", "207243", "207246", "207120", "207121", "207247", "207245", "207125", "206123", "212601", "206127", "206120", "206242", "206124", "250001", "206126", "206128", "201019", "206002", "206130", "206001", "206125", "206003", "206245", "206253", "206131", "212650", "283103", "283205", "283141", "283151", "283207", "283152", "283142", "283145", "283203", "283206", "283130", "283135", "250341", "283136", "283204", "201303", "203203", "245304", "201002", "201008", "201312", "201311", "201305", "203135", "201308", "201314", "201310", "201309", "201313", "203202", "203209", "201306", "201301", "201302", "203207", "201317", "201307", "201304", "250406", "201004", "201007", "250003", "212645", "212651", "204212", "204102", "201103", "281104", "204214", "204101", "281307", "281306", "204216", "204211", "250515", "250002", "201005", "212657", "201204", "124102", "124508", "124202", "124104", "124108", "124109", "124504", "124106", "124103", "124105", "124507", "124404", "124506", "124201", "124107", "124142", "124146", "124505", "250401", "201010", "212655", "201001", "201014", "250404", "201009", "212631", "201013", "212665", "250110", "250221", "212641", "245201", "212661", "201015", "123034", "123303", "123024", "123029", "123001", "123023", "123028", "123501", "123027", "250501", "205304", "205247", "205265", "205267", "205303", "205301", "205121", "205261", "205001", "205268", "205264", "205262", "205119", "205263", "281305", "281202", "281504", "281003", "281004", "281001", "281301", "281404", "281403", "281401", "281005", "281122", "281406", "281405", "281308", "281303", "281501", "281206", "281201", "281123", "281302", "281205", "281006", "281121", "281204", "281203", "281502", "212652", "201006", "212656", "251318", "245301", "251301", "251316", "251305", "251306", "251003", "251327", "251319", "251001", "250106", "247777", "247776", "247773", "250502", "247772", "251311", "251201", "247771", "251203", "251309", "251320", "251307", "251202", "251002", "251310", "247774", "251315", "251314", "251308", "247775", "247778", "201011", "212654", "123021", "123101", "123412", "123103", "123401", "123035", "123102", "123106", "123302", "123110", "123301", "123411", "212621", "250104", "250223", "245101", "245207", "201206", "250005", "245205", "245206", "250344", "201017", "250342", "212635", "201016", "245208", "700022", "734315", "723147", "713429", "723142", "713205", "732124", "721433", "713342", "700029", "713155", "743438", "735219", "743273", "713322", "734218", "735132", "743372", "700117", "742407", "736206", "713154", "736179", "713520", "732213", "722154", "713216", "713140", "743248", "722133", "700155", "712504", "735206", "700094", "713203", "741504", "731236", "721646", "735232", "713165", "733145", "711104", "721633", "722146", "713152", "733128", "743702", "731213", "743376", "741126", "700046", "712408", "721629", "731123", "713404", "700072", "743422", "722121", "712121", "721657", "741156", "700132", "713123", "700060", "741221", "712705", "734429", "736134", "734321", "734423", "713325", "700011", "742308", "713127", "713131", "700028", "731133", "711101", "712515", "736168", "711405", "712415", "721602", "723168", "700033", "742104", "700080", "736122", "723213", "713145", "721303", "742121", "731221", "742187", "712416", "712601", "743232", "731104", "721143", "742408", "733134", "734105", "742303", "721132", "736146", "734436", "722158", "733215", "743221", "721658", "700062", "700032", "734216", "723104", "741404", "711317", "713124", "735135", "743349", "736205", "713519", "721156", "734226", "700133", "735226", "722122", "700119", "741402", "721653", "741315", "734503", "731301", "741506", "743127", "722140", "741316", "721606", "736182", "712702", "721455", "742122", "721306", "713365", "700008", "735216", "712124", "734002", "722204", "722173", "700130", "711107", "741234", "735122", "713419", "713128", "700026", "734013", "732143", "743609", "735233", "723103", "722143", "711302", "743272", "713213", "743165", "713144", "721153", "732121", "711409", "713141", "711414", "721432", "721222", "743133", "742149", "713376", "700030", "711203", "736131", "712704", "700079", "712707", "700098", "722138", "731101", "700048", "733201", "735217", "733156", "721140", "723155", "721422", "721503", "741509", "734010", "700063", "713425", "741162", "732206", "700017", "736101", "713339", "743425", "732128", "736145", "700143", "700082", "713403", "700114", "713372", "741137", "734311", "732214", "743387", "732204", "721467", "721440", "741138", "721632", "713368", "700110", "711103", "721504", "721448", "742238", "712403", "741201", "700083", "743611", "721122", "734317", "735101", "732216", "736207", "700092", "711113", "741103", "700057", "741501", "743136", "722205", "712105", "742405", "713369", "721159", "700127", "713326", "736170", "713340", "735224", "735220", "734004", "743345", "734223", "713344", "713501", "743249", "734011", "713402", "721442", "700084", "700070", "735102", "721253", "700113", "736176", "721148", "734009", "734314", "736156", "711309", "700151", "732125", "743262", "743329", "713211", "713321", "731234", "741150", "711109", "733130", "722139", "700002", "732201", "743399", "721634", "713304", "713208", "700105", "741102", "712503", "700103", "731220", "721515", "731245", "735225", "723133", "721146", "721454", "700135", "734424", "743373", "712513", "742237", "713218", "712137", "700085", "741507", "713167", "700088", "734012", "712213", "712411", "711201", "733103", "700021", "700068", "743710", "713206", "733140", "700158", "722152", "712514", "713359", "731303", "711102", "700123", "721656", "743271", "713315", "742213", "700064", "713360", "732207", "713371", "742134", "722203", "736165", "712502", "713357", "742409", "700115", "732140", "713434", "743318", "741127", "700095", "711310", "723143", "721434", "733127", "743426", "735229", "736157", "712125", "732122", "721260", "743144", "721647", "721513", "736159", "743286", "713341", "711313", "743412", "742236", "713426", "721150", "741161", "712204", "734319", "723149", "712412", "735304", "735227", "743135", "713121", "700093", "713130", "700012", "700142", "731240", "722160", "713333", "711204", "742174", "731242", "721648", "712602", "734123", "742189", "700055", "700089", "741302", "712303", "712249", "700014", "700100", "735208", "723101", "721171", "722157", "700035", "743293", "721438", "711314", "721655", "700091", "735133", "743439", "743235", "741152", "713151", "722137", "713377", "734201", "721643", "700049", "712305", "713405", "713301", "742305", "743610", "722102", "712135", "711403", "731132", "743263", "742159", "721158", "735231", "742140", "713375", "743411", "743222", "731224", "721635", "731130", "732203", "733158", "713147", "731237", "742306", "732126", "712612", "700013", "700150", "721642", "736203", "721430", "731243", "712248", "731235", "721420", "741164", "700102", "712147", "700043", "736167", "713215", "700019", "721127", "732139", "700010", "721458", "721659", "713101", "743377", "741155", "732101", "734313", "721155", "736160", "713148", "733210", "712149", "722151", "743423", "743270", "722206", "741165", "741301", "721437", "723126", "722136", "723201", "712136", "712247", "721160", "700121", "743336", "712134", "713207", "734203", "743126", "712123", "713126", "742226", "741163", "713169", "712250", "741167", "721211", "711315", "732127", "713125", "742102", "743129", "743233", "722101", "700075", "743701", "700147", "713146", "721435", "721628", "700037", "731125", "723152", "700006", "700039", "700018", "721514", "742160", "721101", "741319", "700134", "733124", "721166", "721517", "721631", "734001", "721645", "742225", "711404", "713160", "713363", "734320", "700081", "735228", "743378", "712245", "712413", "712424", "722162", "735305", "741503", "734007", "743356", "711111", "743166", "711307", "711114", "721133", "713347", "735213", "713384", "736133", "711105", "713515", "711305", "721463", "700107", "700137", "713219", "722134", "734501", "721431", "732210", "743252", "741505", "700059", "713428", "732138", "743513", "713129", "736171", "712409", "722207", "700125", "743368", "713422", "743145", "721126", "700061", "700009", "721423", "713122", "711108", "736202", "722202", "700099", "712301", "700042", "711411", "741222", "721424", "712614", "700131", "721441", "721453", "712701", "742166", "743331", "723129", "742113", "735215", "735218", "723148", "723128", "735202", "736169", "731216", "734121", "700097", "721604", "721125", "722132", "743223", "743437", "734217", "721139", "712402", "700067", "732211", "721201", "713427", "734221", "743297", "713338", "700204", "732123", "700149", "735303", "700045", "712233", "700154", "713337", "721443", "712406", "713324", "700148", "713150", "721641", "700044", "743442", "731233", "734014", "712258", "713433", "731201", "743193", "712146", "722149", "732215", "743291", "736201", "734213", "743338", "734227", "723127", "734312", "742136", "700069", "736204", "735207", "741249", "721436", "733125", "743130", "734426", "712308", "721135", "700108", "713346", "731222", "731127", "712426", "721102", "741257", "721445", "741160", "742175", "721152", "742404", "721626", "712708", "732209", "700023", "743312", "721447", "723153", "712223", "743347", "743123", "721650", "700031", "743351", "722208", "712122", "713331", "741255", "700096", "712306", "721429", "742137", "741313", "713103", "741181", "711304", "713302", "733141", "721147", "721507", "742123", "742212", "721426", "735203", "700116", "713502", "700124", "700106", "712102", "721456", "712138", "733126", "723132", "735301", "700111", "741232", "741235", "713162", "721134", "742224", "721167", "700118", "734122", "722142", "721601", "712611", "722141", "712235", "734102", "711112", "700001", "742302", "700122", "712246", "723156", "700041", "712706", "700050", "734015", "723145", "700058", "742135", "743424", "743128", "733102", "731214", "741252", "700073", "721302", "700145", "713430", "713424", "721301", "713381", "741254", "713370", "731223", "734204", "741251", "731215", "741253", "742103", "743704", "700077", "713212", "734434", "741123", "713330", "721137", "732141", "734427", "733143", "732208", "713142", "711316", "713420", "712616", "721425", "713173", "734008", "743125", "735223", "733101", "733129", "731241", "735205", "712148", "700156", "713168", "712221", "734224", "733142", "743337", "735222", "733208", "700086", "723154", "723161", "732202", "743245", "741250", "723131", "733207", "713149", "713214", "743428", "743371", "736121", "742164", "743405", "711410", "735302", "700139", "712615", "700053", "721506", "743287", "712401", "741502", "723215", "721161", "743294", "722183", "742132", "712101", "743251", "743445", "731124", "721304", "721439", "700025", "722135", "711106", "742147", "732102", "743289", "742138", "743134", "721452", "731238", "700027", "713516", "723212", "712405", "700146", "711322", "742202", "742223", "721428", "722144", "700138", "713172", "721136", "712613", "712417", "713378", "713383", "711306", "723146", "743290", "742133", "742201", "741238", "742165", "722147", "734215", "721644", "734316", "743401", "700128", "712311", "742163", "733133", "700109", "732205", "735210", "734222", "742184", "700038", "712202", "732144", "721145", "711301", "741125", "721627", "743370", "743395", "711401", "700071", "743427", "742161", "733209", "711413", "743374", "743502", "713202", "741153", "735221", "741121", "742406", "721446", "736172", "721449", "734005", "721444", "711308", "700051", "741247", "734214", "713156", "734103", "713409", "743446", "734431", "713361", "712501", "742402", "721625", "741256", "721128", "741508", "713158", "712310", "721427", "700157", "700066", "722153", "700129", "700003", "722164", "721603", "700054", "722155", "732147", "712404", "713373", "700004", "713104", "711205", "743268", "713385", "742148", "741157", "711202", "741124", "712304", "700056", "711408", "700024", "734003", "721129", "743247", "721121", "721607", "731302", "741158", "713421", "743435", "733202", "721151", "743355", "734101", "731244", "731102", "712418", "713143", "721457", "713335", "721451", "742101", "741317", "741401", "721654", "713517", "742235", "732212", "713323", "723130", "743613", "721450", "721130", "713102", "712617", "736158", "721516", "712103", "734104", "713303", "734006", "700020", "735212", "743330", "743711", "734301", "743363", "700052", "732150", "700101", "713358", "743348", "713171", "741154", "700136", "700016", "700076", "700078", "742168", "743122", "700140", "732142", "721501", "723202", "743375", "712234", "721242", "743354", "741202", "713157", "743429", "731202", "713513", "711226", "722201", "712232", "713332", "712222", "721636", "700126", "712104", "713204", "741223", "700015", "713170", "713209", "742151", "723121", "700000", "713210", "741122", "742304", "700153", "721401", "734124", "741151", "721505", "700152", "700087", "700141", "722161", "713407", "743503", "736123", "712414", "700007", "713408", "700104", "723151", "700160", "721154", "712152", "700120", "743194", "713334", "700112", "731219", "735234", "713362", "731126", "712419", "722150", "712302", "713386", "741140", "743124", "735209", "721212", "741139", "743456", "741246", "736135", "743332", "741159", "721131", "722156", "735230", "736208", "712201", "733121", "735134", "712203", "711412", "700005", "734421", "742401", "711110", "741101", "713406", "700065", "731103", "731204", "723102", "713166", "743292", "734220", "741245", "713380", "735204", "721651", "700047", "713423", "742301", "721305", "741248", "711303", "741166", "735211", "743504", "712407", "713512", "712139", "700036", "713217", "711225", "735121", "733123", "721149", "713514", "731121", "713401", "732103", "711312", "700159", "743357", "700074", "700090", "733132", "721232", "712512", "721124", "731129", "735214", "743383", "721144", "743234", "713432", "700144", "700040", "713343", "700034", "713201", "713305", "734219", "712410", "713336", "713153", "711227", "721649", "722148", "721157", "721172", "742227", "721652", "201318", "700161", "122012", "201318", "700161", "201318"], "exlcude_cities": []}, "groups_name": "Ashish - North"},{"id": 237, "group_data": {"cities": [], "states": [], "pincode": ["495116", "493195", "494221", "496554", "492013", "493996", "496225", "492012", "490023", "496331", "491223", "495009", "493228", "491885", "493885", "495334", "491559", "493331", "496111", "494223", "491107", "491558", "490011", "494447", "493441", "494115", "496445", "497118", "496242", "497773", "491666", "495557", "495003", "496109", "495119", "494665", "497101", "494635", "491229", "494001", "491441", "493101", "495688", "493558", "495115", "495001", "492015", "497231", "491445", "495004", "493344", "492006", "493225", "495555", "490006", "494333", "493888", "494551", "493773", "494444", "494335", "494228", "495552", "494111", "494337", "495686", "495447", "496107", "495117", "493118", "495663", "496100", "494222", "496334", "495559", "492010", "495695", "491881", "491332", "493559", "494224", "491665", "492002", "495455", "497448", "495689", "497220", "493555", "493449", "493770", "490020", "494661", "495674", "494442", "494114", "495683", "495661", "491226", "497335", "491227", "493221", "494556", "496665", "492008", "493448", "495449", "497778", "496661", "491335", "495556", "497446", "497339", "496118", "494669", "493662", "492003", "495448", "495550", "496336", "496223", "493338", "493229", "497331", "495112", "495220", "493445", "497447", "494010", "494448", "496551", "490021", "497559", "495691", "496115", "494449", "495330", "495554", "491993", "491661", "497235", "493114", "495223", "491444", "497442", "495006", "494334", "495551", "490022", "491331", "493222", "497111", "492112", "491228", "494441", "493196", "493661", "490026", "497449", "496108", "494446", "495450", "493776", "494776", "497557", "492099", "493887", "493111", "491557", "490024", "495677", "491336", "496450", "494777", "495222", "496224", "495224", "490025", "492014", "495690", "494122", "497333", "495452", "495446", "495692", "490042", "493663", "496001", "496440", "494771", "491337", "491771", "496116", "491001", "494450", "495553", "496113", "491995", "494331", "494670", "490001", "495445", "494230", "493335", "496338", "495454", "493526", "497226", "495687", "492009", "495113", "490009", "492101", "497555", "490036", "492001", "497229", "491111", "496245", "497116", "491668", "491221", "493890", "493332", "493889", "497001", "493778", "495118", "494552", "495671", "491888", "497225", "497223", "497119", "495335", "497117", "496220", "494229", "492004", "495682", "492109", "494226", "491340", "493992", "493116", "493881", "494336", "497553", "491222", "496227", "497114", "493891", "496330", "495660", "491338", "497224", "492005", "497451", "494553", "495442", "495684", "491225", "494347", "493113", "495668", "496005", "493554", "493551", "492007", "396240", "396193", "396215", "396210", "362520", "396220", "362570", "362540", "403802", "403731", "403510", "403501", "403203", "403104", "403201", "403114", "403515", "403714", "403726", "403801", "403115", "403706", "403712", "403106", "403715", "403001", "403102", "403705", "403517", "403725", "403402", "403704", "403717", "403529", "403508", "403404", "403804", "403709", "403507", "403107", "403105", "403511", "403110", "403505", "403806", "403513", "403506", "403202", "403702", "403512", "403716", "403722", "403723", "403710", "403530", "403410", "403527", "403502", "403728", "403406", "403005", "403401", "403516", "403004", "403521", "403711", "403721", "403204", "403006", "403504", "403509", "403103", "403002", "403526", "403729", "403109", "403523", "403720", "403101", "403701", "403503", "403108", "403206", "403409", "403724", "403707", "403524", "403601", "403403", "403803", "403718", "403713", "403602", "403703", "403708", "403719", "391410", "395004", "388265", "380063", "362229", "380061", "387430", "384320", "383260", "364070", "382423", "388230", "361005", "382433", "362269", "364530", "363430", "394270", "365640", "388210", "361320", "380015", "396230", "388370", "394690", "388360", "396145", "382213", "364313", "384003", "391430", "388245", "361280", "393155", "388620", "396195", "385210", "388480", "360070", "385555", "392230", "394210", "390010", "396427", "387610", "380013", "361009", "360035", "370135", "394430", "362630", "370455", "384012", "364465", "387380", "394310", "382815", "391340", "392155", "396350", "385540", "388110", "389340", "364110", "384360", "387370", "364003", "388710", "370230", "360452", "382007", "393041", "391740", "364710", "390006", "387355", "396580", "370405", "370203", "382260", "390001", "388510", "382320", "385570", "370485", "362245", "364515", "389151", "360530", "385350", "396310", "370670", "383421", "382001", "361007", "389220", "388235", "382170", "388520", "383110", "370650", "392170", "382463", "393145", "383345", "396530", "390016", "360440", "383317", "363642", "394601", "360520", "396050", "360515", "382045", "382150", "385340", "389341", "388630", "391760", "394518", "388543", "365435", "388713", "382455", "396150", "363330", "361250", "385505", "387120", "382345", "364050", "391150", "361160", "387360", "388460", "394370", "382010", "390003", "363520", "396375", "396433", "394115", "394327", "365450", "396450", "396001", "364145", "396570", "364720", "382230", "394120", "365620", "392210", "364510", "390024", "396171", "380052", "385310", "383250", "384210", "362315", "382470", "370665", "392240", "362235", "391115", "380043", "389110", "382729", "382825", "393001", "384335", "391510", "382330", "393125", "396445", "391750", "370645", "362730", "360531", "384230", "362001", "360370", "385506", "364310", "385410", "382715", "384325", "394345", "392040", "364210", "393002", "382845", "384212", "370140", "364002", "382145", "370155", "395008", "360590", "362110", "388250", "382418", "361335", "388335", "380004", "387340", "361110", "385130", "394440", "362030", "370425", "384435", "382501", "387570", "380006", "363630", "391110", "363002", "394651", "362150", "382780", "382245", "360005", "362265", "389140", "383310", "384260", "389152", "370435", "388225", "396380", "388450", "382850", "384315", "396521", "370165", "370490", "364120", "396321", "394410", "364270", "362263", "394340", "394330", "364250", "382016", "382006", "362715", "388590", "396065", "388260", "382475", "360430", "364490", "395007", "364765", "362120", "396235", "362650", "394530", "389146", "392035", "382130", "360510", "391330", "382265", "388365", "361013", "388410", "370020", "394360", "370415", "394660", "363030", "393017", "383246", "360030", "394111", "392215", "360001", "370205", "364006", "388130", "394810", "382315", "383350", "382730", "382424", "394635", "380054", "360460", "394235", "365440", "360577", "387650", "388550", "360023", "394185", "364260", "396126", "370421", "393130", "362135", "394125", "362250", "391135", "364750", "391145", "387305", "389350", "362140", "382840", "394445", "392140", "382443", "394130", "382740", "382735", "396540", "382042", "365660", "394245", "395011", "364150", "394325", "391121", "370030", "394730", "382165", "383325", "395003", "380055", "380014", "362227", "382120", "360040", "383270", "382775", "396115", "361142", "362625", "389001", "382428", "394550", "384272", "387530", "388150", "382340", "360375", "362037", "360002", "394107", "391243", "387635", "389230", "383006", "365550", "387640", "388350", "389232", "380050", "382308", "395012", "387520", "364295", "389310", "361011", "394305", "388330", "360380", "384410", "383251", "390011", "362640", "385520", "360410", "389235", "389320", "396418", "391530", "394655", "383255", "380058", "388239", "382705", "389210", "394160", "361140", "364230", "394326", "363530", "380028", "389002", "370240", "395013", "384241", "361006", "363427", "389382", "391810", "385560", "393135", "396325", "361330", "382449", "394352", "360110", "394720", "396370", "364004", "394101", "394650", "387130", "380023", "382435", "383276", "396105", "382640", "391745", "370130", "370427", "364140", "360578", "387210", "361130", "382732", "362725", "360320", "390025", "384205", "396403", "360330", "383434", "383010", "360421", "360021", "362015", "388315", "384430", "393010", "380009", "361012", "364280", "390021", "360055", "383440", "361220", "391774", "392020", "382855", "362610", "383245", "392025", "370475", "370602", "385550", "390007", "396421", "361310", "383001", "383120", "383450", "383210", "396409", "392135", "383355", "391250", "360022", "396510", "387220", "382865", "391130", "380022", "383315", "385001", "382110", "363621", "383235", "391440", "394190", "396170", "370115", "360570", "365610", "380001", "364505", "380008", "387315", "389250", "365630", "370615", "396469", "388121", "394716", "387560", "396130", "383462", "394246", "388310", "365456", "396045", "394180", "396590", "394140", "394221", "365480", "382630", "396120", "396051", "361350", "370620", "383320", "390018", "362215", "384120", "388530", "388307", "362530", "370110", "364760", "388610", "388205", "361210", "388430", "385110", "396165", "394350", "384160", "387350", "364320", "395009", "389180", "388120", "363110", "388355", "382721", "384130", "362002", "391240", "363035", "362560", "370601", "385535", "391107", "370655", "389154", "365635", "384255", "385135", "384225", "382024", "361003", "382405", "363115", "388325", "384330", "396180", "382481", "360006", "388440", "392310", "389370", "389115", "391350", "387365", "384140", "362004", "388470", "388625", "365601", "382355", "396385", "391170", "384265", "394163", "396360", "388540", "388640", "362225", "393151", "370210", "382620", "382430", "383225", "382321", "370001", "392012", "361141", "361150", "396030", "360060", "388345", "360025", "361002", "320008", "370450", "364265", "362230", "363435", "394421", "387540", "363423", "362020", "365560", "390017", "391775", "391310", "391160", "365540", "384151", "391156", "383316", "393050", "380007", "387411", "394170", "394515", "384001", "360360", "382427", "390008", "364525", "394230", "387001", "361306", "382810", "365565", "389390", "394375", "370510", "391210", "380038", "361347", "391220", "382460", "362550", "388160", "388340", "363670", "396412", "362510", "394105", "388421", "396415", "382870", "388305", "388220", "382250", "382480", "385510", "394240", "380051", "363310", "394150", "363410", "365645", "370040", "365421", "382765", "389330", "388180", "365650", "364330", "390020", "361345", "380019", "365545", "387345", "360540", "384215", "387335", "363320", "382422", "370145", "382021", "393150", "383307", "382115", "391345", "360550", "363650", "396155", "396110", "392015", "396460", "365460", "383430", "383460", "391125", "389120", "370625", "392220", "394317", "382350", "391175", "392160", "396436", "384240", "391168", "391770", "384245", "380005", "385530", "382415", "393191", "394250", "396466", "394640", "382820", "395023", "360003", "395002", "382816", "364740", "364730", "387710", "390012", "385565", "365430", "380016", "387310", "390022", "360405", "382860", "388270", "394641", "394710", "370605", "363510", "394315", "385120", "389240", "394320", "394516", "391320", "370675", "394355", "363415", "360024", "391152", "364005", "382140", "384355", "388001", "391155", "394155", "396560", "384345", "383230", "361008", "383275", "388170", "362255", "392110", "385010", "393020", "391105", "362268", "390002", "362710", "385575", "370465", "390013", "393140", "382255", "382210", "362276", "360470", "385515", "396475", "362565", "394335", "364521", "392180", "382745", "383330", "387320", "396125", "360050", "396424", "370430", "384221", "394633", "392165", "362011", "380021", "370460", "360480", "396430", "396135", "394620", "396439", "382220", "370511", "395001", "365541", "383030", "380027", "382760", "364275", "385421", "385320", "362720", "388465", "382041", "370630", "382650", "380057", "394517", "385330", "364485", "393105", "394365", "370445", "382755", "380026", "382835", "361305", "391101", "394248", "382425", "394715", "389265", "389175", "396440", "385545", "391780", "384421", "382421", "383220", "383305", "391165", "393025", "362310", "382445", "384310", "389360", "387115", "391421", "364130", "392130", "396463", "363040", "362130", "391140", "388545", "391761", "394043", "384290", "394520", "360575", "370410", "370660", "388320", "393040", "361315", "390014", "362240", "387240", "382030", "383205", "396472", "396007", "364470", "382440", "390019", "360020", "387230", "382830", "364290", "365555", "370627", "362275", "385360", "390009", "362226", "383410", "361230", "370640", "395017", "387325", "360007", "380081", "388560", "360311", "391520", "387550", "389380", "384340", "387375", "382465", "396020", "364001", "361170", "370105", "363351", "383215", "364522", "383240", "360560", "360490", "380059", "393120", "390023", "370160", "389172", "365455", "384275", "388580", "383340", "365410", "395101", "396060", "391244", "389190", "392001", "396185", "393115", "390004", "394510", "384170", "393030", "383422", "382725", "388306", "370015", "387002", "384305", "387630", "384246", "392150", "396035", "389160", "384110", "362266", "361001", "364240", "360545", "361325", "396406", "394680", "363020", "394380", "363655", "360576", "362620", "363425", "394670", "387620", "370150", "360004", "396002", "392030", "396191", "389170", "362260", "389260", "382028", "384285", "384002", "383335", "395010", "380018", "380060", "382225", "361010", "391120", "360450", "361004", "364060", "364135", "382728", "363440", "391445", "391346", "363421", "391450", "396040", "365220", "363641", "382610", "388215", "382710", "361120", "387510", "364081", "395006", "389155", "388570", "382750", "389130", "382450", "387330", "380024", "361240", "389365", "380002", "362222", "370201", "387110", "394116", "362220", "384229", "394630", "393110", "365535", "384220", "394110", "388255", "394405", "392011", "361162", "395005", "382305", "382240", "388140", "396055", "396140", "370610", "394540", "363001", "385566", "362205", "363660", "360579", "475002", "465669", "455001", "480994", "456224", "457555", "474015", "456770", "456222", "477117", "476335", "480111", "483773", "480447", "453331", "480555", "470232", "487551", "472447", "480001", "451660", "457340", "455332", "450117", "472115", "484887", "453112", "480882", "480553", "471311", "481111", "482020", "452005", "481666", "458553", "471525", "486223", "475686", "477116", "453332", "488448", "451332", "473444", "487001", "457990", "458880", "473330", "454010", "486775", "452013", "462047", "457888", "480108", "488442", "477222", "472442", "460665", "458001", "480661", "485226", "487770", "481664", "474008", "473001", "464672", "452004", "454116", "457331", "470227", "487441", "461005", "473249", "462101", "473118", "483504", "471625", "487334", "472336", "483330", "470002", "481771", "485441", "460443", "481224", "488333", "465226", "455115", "470669", "458228", "480996", "480331", "476332", "484770", "471111", "453115", "470221", "486661", "454449", "465333", "451221", "465118", "457885", "456664", "471105", "457777", "470335", "475220", "488222", "460110", "480110", "453556", "474007", "473662", "453555", "460449", "465691", "486888", "485775", "462004", "451111", "480990", "480105", "481996", "465445", "482002", "476355", "473113", "485551", "462041", "460668", "460663", "471405", "484334", "480112", "464665", "456337", "486333", "480887", "486111", "461551", "480557", "481672", "481768", "451228", "462038", "484220", "488446", "483770", "484776", "474003", "481449", "471408", "482021", "466651", "470120", "453220", "487225", "450661", "485666", "456665", "487337", "461661", "453661", "484665", "475330", "470119", "470664", "457118", "486001", "470124", "480106", "486666", "482011", "487330", "453441", "470125", "473880", "461881", "454775", "454441", "477449", "473115", "461771", "460553", "458895", "454446", "452017", "480224", "487661", "470118", "458669", "454221", "462043", "476337", "471510", "477331", "481998", "450332", "458778", "481882", "486114", "456335", "458339", "455116", "450445", "471501", "466331", "460220", "464776", "453771", "486335", "482010", "486338", "473110", "465697", "466125", "476444", "457226", "475673", "451442", "470441", "460551", "486003", "450337", "482001", "484774", "477447", "484555", "480338", "470337", "473665", "466661", "475685", "473865", "481885", "456441", "474004", "457119", "476001", "470772", "454335", "485001", "470229", "456668", "466111", "462036", "457441", "486115", "458330", "481335", "463106", "465550", "452010", "472118", "473446", "464224", "485772", "464881", "484440", "452009", "458664", "481662", "456776", "465441", "473585", "483119", "453111", "484336", "451666", "475335", "477446", "485773", "473670", "480223", "481102", "452016", "473774", "483442", "487114", "465447", "480449", "466001", "462002", "476221", "456221", "466114", "486446", "462007", "464228", "473885", "480334", "464993", "483336", "452003", "451220", "473775", "457882", "464886", "486220", "472001", "486886", "480771", "457779", "472101", "485112", "475682", "487221", "461668", "477105", "476554", "465674", "480880", "460225", "461110", "454001", "470051", "486771", "451440", "484113", "487110", "473335", "464114", "462003", "458336", "486670", "470672", "454665", "485771", "482009", "454773", "475661", "464671", "470673", "465335", "465667", "486885", "456010", "465116", "483220", "464258", "460440", "480881", "485005", "485881", "451881", "480337", "452007", "486117", "472331", "472246", "460447", "481884", "481556", "450116", "485334", "473112", "481115", "481778", "455440", "483113", "462026", "452001", "462039", "483110", "486123", "457775", "473638", "477557", "473105", "477660", "464331", "477332", "462120", "450001", "463111", "462011", "486884", "475336", "456001", "486889", "451224", "450221", "450554", "470223", "473793", "450771", "484664", "480107", "470666", "488441", "473443", "473331", "457772", "465113", "466554", "465220", "462042", "488443", "471515", "481222", "484001", "485111", "462027", "452014", "476115", "466116", "486005", "455227", "461775", "483105", "484120", "461116", "484669", "464668", "460001", "464337", "476111", "464884", "462010", "465687", "452002", "470115", "484116", "462020", "473440", "456661", "464001", "474010", "450881", "486006", "483222", "472337", "480441", "481551", "486441", "462066", "451551", "454331", "476224", "471001", "488050", "470003", "458558", "450119", "481663", "481995", "484444", "464240", "484771", "465679", "462001", "477445", "457550", "484110", "465339", "474020", "471606", "450114", "482005", "472221", "473101", "482051", "477333", "458220", "474005", "483225", "458110", "451225", "462024", "473551", "470113", "465001", "457661", "470880", "481226", "460004", "485331", "458389", "480997", "451335", "481117", "461446", "485114", "471101", "450991", "458990", "455223", "487881", "484881", "483332", "455111", "484330", "464770", "465110", "456331", "481105", "450051", "472010", "465227", "461115", "481441", "486445", "480999", "451331", "486890", "484224", "465230", "465683", "453551", "470004", "470771", "466221", "454552", "465685", "462030", "476339", "464221", "455118", "450110", "485778", "486556", "455459", "483001", "485113", "483331", "476134", "476219", "470117", "451001", "477566", "474002", "485661", "458667", "465693", "470442", "473287", "482004", "475115", "473781", "481331", "471318", "486553", "455221", "452006", "464774", "456440", "481001", "471411", "481116", "457333", "470021", "475001", "471313", "466115", "455339", "486447", "474011", "481665", "450331", "477001", "461221", "464111", "480888", "450551", "480884", "474009", "472111", "488001", "480109", "481661", "456003", "461111", "458226", "485115", "481337", "480886", "454660", "485774", "470775", "484552", "453446", "486881", "480551", "473226", "470675", "462022", "240693", "470881", "451556", "473660", "471315", "487118", "465661", "473995", "451449", "484886", "455336", "477335", "484661", "482003", "481880", "462046", "456313", "464990", "472339", "452011", "466445", "483053", "474001", "452012", "462031", "458556", "451115", "481776", "461223", "486550", "486450", "475671", "481668", "458883", "470001", "477111", "464220", "464661", "461990", "474006", "486887", "486340", "473111", "483880", "481332", "483990", "481051", "464651", "473332", "486448", "481990", "454111", "484446", "486776", "462045", "473222", "485446", "458775", "456443", "486341", "471201", "465223", "460666", "462044", "482008", "476228", "470235", "480991", "466120", "484117", "488059", "483775", "451010", "483501", "466118", "460330", "476229", "470339", "481879", "484660", "465680", "457339", "470661", "460554", "483334", "461331", "480003", "450112", "451441", "488051", "457773", "477555", "454774", "457770", "471516", "486669", "458113", "473990", "475110", "485221", "460557", "470226", "466665", "472445", "457993", "462033", "484114", "457887", "464226", "475675", "456006", "461122", "458468", "474012", "456771", "488220", "456550", "465449", "462023", "486002", "482056", "461001", "458116", "486892", "466446", "462037", "461114", "458441", "462013", "464113", "451770", "471301", "483440", "486226", "464986", "466113", "457114", "477441", "452020", "470228", "480559", "457001", "457222", "486331", "472446", "480667", "466448", "458888", "465337", "458118", "465677", "486675", "457336", "465106", "451447", "470663", "452015", "458002", "486882", "487555", "451113", "486440", "453001", "452018", "465689", "473770", "481445", "480221", "458771", "458470", "464551", "485447", "486451", "461441", "484551", "475005", "472005", "462008", "462016", "480115", "460661", "461228", "477227", "425503", "443001", "441225", "444107", "444705", "415313", "411017", "413402", "431542", "412211", "441905", "442305", "411047", "423702", "400065", "422620", "424101", "423110", "425506", "414401", "400006", "445202", "413726", "413580", "400071", "400612", "444709", "400062", "400074", "411041", "412308", "425105", "444902", "415106", "431541", "402201", "441301", "431514", "400056", "425413", "416523", "413602", "411109", "431008", "431723", "413525", "442003", "431718", "411604", "401403", "442303", "415726", "444723", "440004", "411005", "444606", "410221", "413106", "431002", "410511", "413206", "413209", "413116", "443401", "413502", "415211", "442111", "444711", "416604", "415718", "415710", "431004", "411032", "425305", "416230", "415619", "402102", "423701", "440024", "413305", "431154", "402117", "400608", "441212", "423501", "412410", "401203", "402126", "411008", "441105", "413403", "400080", "416315", "413401", "416520", "416104", "416506", "415023", "442502", "431803", "441304", "441207", "425505", "412412", "411048", "413516", "415409", "443308", "440023", "401208", "416417", "444908", "410516", "416806", "444108", "441226", "441912", "414603", "415408", "421003", "400017", "414305", "444603", "441924", "445001", "400607", "431205", "400010", "413606", "445210", "416407", "400093", "424311", "444901", "425427", "416401", "415620", "424102", "416116", "442901", "416626", "410210", "415107", "416504", "445230", "424305", "413111", "416614", "425411", "425415", "416229", "415101", "423104", "401502", "425502", "400094", "441112", "400033", "415702", "431713", "416118", "415604", "423212", "411024", "413608", "401603", "421301", "444809", "415719", "400040", "440006", "413212", "443304", "425102", "415722", "424306", "413306", "411018", "441205", "431107", "416611", "442202", "440035", "400018", "444304", "422306", "415602", "415001", "415111", "400104", "413523", "422604", "441204", "415004", "400096", "431127", "445401", "413229", "402103", "416405", "441806", "400083", "441209", "416608", "425444", "412212", "421203", "415641", "410401", "413508", "425104", "400602", "416416", "416004", "413711", "415011", "442501", "444104", "443106", "415706", "413133", "442917", "411004", "416411", "440012", "400024", "423402", "402308", "400045", "416412", "413213", "400709", "413504", "415214", "400036", "402402", "400039", "425501", "413534", "410209", "414502", "416013", "441123", "441601", "411016", "414505", "416518", "415307", "431703", "425416", "413701", "414101", "415628", "413719", "423601", "441302", "415213", "422209", "440013", "414602", "441903", "421308", "442903", "425116", "413004", "442402", "400102", "444312", "413120", "402204", "441215", "416119", "441108", "416807", "440008", "431009", "421506", "444126", "424207", "431501", "410510", "441805", "441908", "431811", "416205", "414604", "410202", "414302", "410101", "441909", "431715", "444804", "443206", "415802", "431809", "423303", "400005", "416502", "413603", "400042", "444702", "413409", "416410", "402208", "415634", "413002", "444717", "413103", "413107", "400075", "444506", "410513", "412103", "416209", "431513", "416437", "416143", "415701", "415538", "414607", "444810", "400078", "442507", "415805", "415310", "415405", "416606", "415728", "401204", "440010", "431110", "414208", "431142", "422012", "443402", "421302", "415209", "431510", "414105", "444109", "440015", "415402", "416534", "413723", "442503", "413112", "416418", "444001", "412205", "444806", "441401", "445204", "416415", "413601", "413130", "414503", "416512", "401103", "431206", "424001", "425421", "402304", "415539", "411051", "410509", "415311", "411001", "414501", "421202", "444803", "431209", "431506", "415124", "416712", "400059", "412306", "416550", "431202", "400025", "412806", "416312", "444202", "400048", "413219", "442101", "424307", "431125", "420003", "431807", "425401", "416516", "411027", "421002", "431522", "416525", "445323", "423105", "416215", "416311", "412108", "445002", "400086", "401501", "416513", "400603", "400069", "416501", "441306", "425508", "431704", "412105", "422011", "441101", "423208", "413737", "415537", "444906", "421401", "414202", "442104", "411043", "415713", "400702", "412805", "416524", "413406", "441303", "415640", "431606", "445211", "416304", "400037", "416120", "413410", "431741", "412303", "400098", "425524", "416510", "415606", "442709", "416012", "413255", "421505", "416515", "442304", "416144", "431131", "442904", "401207", "431705", "422221", "441222", "413215", "402207", "424203", "424103", "441807", "416310", "413713", "413802", "401202", "444203", "440019", "431743", "411026", "431601", "442406", "441305", "442105", "402403", "441221", "431521", "416107", "413104", "400054", "400095", "425109", "416122", "416105", "414201", "412304", "411021", "416705", "415306", "415626", "425428", "431511", "431701", "415207", "440032", "400012", "402111", "423117", "412112", "416236", "425414", "442710", "413710", "400029", "412801", "400041", "411045", "416701", "402203", "413517", "401604", "421005", "400020", "440003", "400070", "425405", "401301", "411022", "415506", "401304", "400015", "414002", "413412", "415122", "411046", "412214", "416302", "431711", "442307", "416314", "402104", "440005", "413003", "411061", "443301", "410203", "431502", "410503", "440017", "422204", "400707", "416206", "415516", "423403", "416517", "442306", "400067", "413739", "424004", "411033", "416813", "441111", "444710", "400026", "412405", "400601", "410512", "416623", "416713", "413304", "413222", "402404", "431402", "411030", "415540", "431208", "400092", "423101", "441107", "422601", "401602", "425301", "444708", "400072", "445109", "400082", "431148", "400016", "422007", "423109", "415411", "425309", "416413", "416419", "422112", "400976", "415108", "444407", "416308", "441501", "431746", "410502", "440026", "442902", "401504", "440030", "441803", "442001", "400099", "415401", "424302", "410216", "400089", "411038", "444403", "410301", "431401", "401105", "400708", "414001", "441122", "411012", "402101", "413307", "422208", "416610", "416509", "416103", "441110", "416519", "441901", "431810", "413411", "444401", "400049", "402307", "402309", "415637", "415615", "416306", "422602", "423202", "416632", "441502", "401209", "415608", "415407", "400087", "412408", "413207", "422102", "400055", "445209", "442606", "413404", "410505", "423602", "412803", "400034", "415714", "431604", "413114", "402125", "413706", "413720", "444110", "415502", "413101", "402305", "416409", "414111", "423502", "422502", "400004", "441203", "414204", "440007", "416528", "402209", "412209", "425417", "410501", "431720", "444905", "416805", "431114", "401402", "442302", "415605", "440027", "416521", "431213", "415526", "440016", "440036", "413721", "413252", "413712", "411034", "416811", "416008", "410204", "425327", "441214", "400028", "422501", "410506", "400097", "413210", "410206", "444409", "414005", "402108", "414601", "416223", "444303", "413220", "411052", "413528", "416003", "415806", "413501", "400007", "412102", "402302", "414701", "416605", "424208", "412114", "416609", "400085", "416010", "401703", "415616", "425114", "415536", "444405", "425507", "412220", "400614", "442403", "415717", "444903", "413228", "416309", "431512", "411025", "421603", "413728", "401607", "425432", "416115", "445110", "431101", "413303", "444105", "415015", "422603", "421103", "410222", "411036", "442603", "401404", "422303", "413715", "416307", "431104", "421503", "442906", "431115", "415208", "416208", "400077", "415109", "415807", "445103", "422605", "415303", "441113", "416526", "411007", "416106", "400060", "414006", "441902", "443104", "400076", "441804", "400081", "400606", "425407", "416216", "431507", "444006", "415517", "411006", "414205", "425423", "445106", "416527", "415501", "445306", "443201", "444003", "444510", "416112", "413544", "415528", "422001", "444502", "413250", "400050", "413519", "422213", "413102", "416601", "415509", "416503", "414504", "415305", "412305", "400008", "416232", "425308", "400014", "440021", "400706", "431203", "413109", "415521", "445102", "416211", "400030", "413302", "431134", "413521", "422611", "416218", "415212", "415410", "416102", "411044", "443403", "416408", "415203", "414106", "400051", "413522", "431717", "415520", "411015", "415010", "425420", "442905", "400710", "415403", "431143", "431128", "413208", "400058", "431215", "415803", "414605", "400047", "424109", "413205", "416531", "443404", "402303", "431126", "415413", "425304", "431207", "400084", "423107", "422105", "425003", "442605", "415530", "423206", "415524", "431536", "410302", "400091", "415020", "416612", "444719", "416551", "441223", "400068", "425403", "413581", "413214", "416804", "424106", "401503", "444102", "421102", "402114", "413514", "415110", "416628", "413249", "444501", "416101", "425111", "413605", "412219", "423213", "413309", "416110", "412301", "444002", "413007", "431133", "445201", "416305", "442908", "416001", "411062", "445101", "415312", "415301", "422301", "423302", "422013", "410402", "400001", "413512", "444605", "412107", "400043", "444004", "443303", "411058", "441103", "444720", "431120", "422401", "425113", "431505", "440034", "411037", "415508", "424002", "443112", "441104", "425424", "411057", "431136", "431508", "441001", "413310", "414103", "422606", "421602", "400701", "440018", "416529", "431804", "421601", "416801", "415406", "441911", "442707", "415414", "424303", "443002", "444805", "442201", "445301", "444604", "416549", "400031", "413509", "422211", "413705", "413725", "442203", "443102", "413223", "413738", "425504", "416214", "423205", "413005", "416511", "443203", "412207", "416420", "444807", "441904", "443202", "412802", "414403", "400061", "415603", "423301", "422302", "425303", "425307", "416507", "410515", "412215", "413582", "415612", "413224", "431150", "431750", "431121", "442907", "414609", "416221", "445216", "400009", "425311", "416002", "416406", "412115", "421004", "415104", "440014", "424107", "412206", "444904", "415003", "442703", "411019", "444701", "413118", "425201", "413322", "431805", "415601", "401305", "412208", "413530", "444302", "425426", "444808", "431719", "412401", "410208", "431123", "416603", "444801", "410207", "416436", "422222", "410201", "431540", "422002", "444301", "400088", "413505", "411002", "416615", "414110", "411020", "444106", "413008", "423607", "431144", "400066", "400063", "411013", "400610", "444504", "415412", "412203", "400703", "431504", "415022", "413527", "422004", "431503", "425112", "413217", "441701", "445207", "402106", "424005", "413203", "421201", "443101", "441913", "425404", "441206", "442704", "410102", "413113", "421502", "431710", "416508", "416514", "424119", "415523", "414301", "422205", "440020", "415613", "412210", "415709", "444601", "415103", "431515", "414606", "401210", "415715", "431153", "442301", "431129", "416146", "412409", "402116", "424204", "416201", "416616", "431103", "442106", "415510", "413506", "444511", "413526", "400021", "410406", "431001", "415522", "423603", "431712", "444907", "443204", "413709", "400002", "411039", "442505", "410508", "416404", "431806", "431214", "414113", "415315", "413251", "441224", "422210", "445307", "416703", "410403", "413105", "431010", "421312", "442504", "431211", "441106", "444117", "423108", "415115", "415729", "442701", "415014", "401405", "412202", "441208", "415711", "431745", "413736", "424105", "416602", "413708", "425306", "416552", "400019", "402202", "413204", "415610", "400003", "412106", "431509", "444404", "422206", "413702", "402115", "443103", "413405", "401506", "421305", "416630", "422008", "413532", "410218", "415309", "424206", "423106", "431722", "416113", "425418", "400090", "424308", "412312", "425101", "445402", "441907", "412403", "425412", "415703", "431003", "423401", "401609", "425004", "443302", "413115", "442914", "413216", "413707", "402112", "415727", "423204", "422403", "441210", "441801", "431802", "425419", "431147", "413531", "431537", "440022", "416301", "413604", "431105", "416402", "402301", "414306", "444507", "445304", "413226", "422201", "401401", "400052", "423604", "413110", "413524", "425406", "445203", "415206", "411028", "402401", "424304", "415404", "416707", "421001", "422622", "444813", "410220", "410504", "415724", "431716", "425402", "400057", "444607", "422215", "413714", "415705", "444101", "444306", "421101", "413324", "431124", "441202", "414402", "422402", "415515", "415012", "415720", "415512", "445305", "413211", "423201", "413202", "440001", "422103", "415205", "421605", "415304", "442604", "412104", "444802", "412110", "423203", "415504", "413132", "442102", "416403", "402122", "400705", "415308", "415019", "402107", "424309", "411003", "441914", "414102", "413718", "416005", "422608", "422610", "411009", "444311", "416505", "422212", "413510", "431605", "445105", "424201", "401302", "444706", "413503", "401608", "442916", "445205", "424318", "416303", "431006", "415611", "401101", "444111", "423605", "444402", "444005", "423111", "416810", "412213", "421402", "425103", "416111", "416414", "415643", "416202", "442401", "412201", "431212", "402120", "441906", "431118", "413624", "411060", "413308", "441404", "413511", "415519", "445206", "415609", "412109", "416803", "424310", "416213", "416220", "413108", "415607", "445308", "401102", "413515", "442702", "415621", "410507", "411031", "421303", "411042", "415801", "444704", "422003", "416203", "416006", "431530", "400044", "413801", "431518", "416210", "422305", "400605", "431520", "415102", "415617", "425442", "415716", "431707", "424205", "422207", "441802", "431517", "431137", "413518", "416234", "421501", "401606", "431736", "402113", "425115", "411040", "411035", "441809", "413623", "423703", "441702", "444204", "445302", "444103", "415505", "431116", "412406", "413001", "415507", "413722", "415527", "441109", "444503", "431519", "401610", "415639", "401201", "416108", "424108", "445215", "422006", "415712", "413248", "422009", "400027", "422304", "412804", "425108", "445303", "421403", "413201", "416114", "401702", "431709", "414304", "414303", "415013", "412402", "415021", "415503", "424006", "415105", "422005", "401605", "431122", "415511", "415804", "415514", "400704", "413227", "425002", "425110", "416613", "400064", "416316", "400023", "416011", "400035", "415002", "431523", "413314", "442404", "415114", "411011", "402110", "431602", "401206", "415302", "410205", "422104", "413218", "413221", "431106", "431130", "441915", "441217", "415513", "425422", "413703", "414203", "415112", "431721", "413319", "401303", "425410", "421306", "413717", "413607", "413520", "400615", "431135", "416812", "416109", "413253", "415518", "422101", "421204", "421311", "413716", "415202", "424104", "410014", "416235", "416231", "425409", "431117", "422202", "425310", "415415", "440037", "425203", "441910", "412216", "425408", "410405", "431113", "416522", "411023", "425302", "416313", "413315", "415525", "431007", "425452", "424202", "413507", "422203", "401601", "431516", "415629", "422010", "415116", "431151", "423102", "422113", "431808", "431132", "440033", "400022", "440002", "431111", "416704", "413317", "402109", "402306", "416007", "441614", "441102", "415730", "431112", "416219", "416204", "431742", "444602", "416702", "442918", "444505", "415614", "440025", "412101", "416709", "400032", "401701", "431714", "431102", "444201", "431801", "413704", "400013", "431005", "422308", "413529", "441916", "401106", "413006", "400011", "412311", "400079", "416620", "431702", "412404", "400604", "412218", "412411", "413301", "416121", "441201", "431603", "400038", "401107", "412204", "415708", "413513", "400101", "414003", "431204", "400103", "424301", "416212", "440009", "442705", "425107", "411014", "431109", "425001", "402105", "431708", "416207", "412307", "431731", "422214", "444707", "400053", "384445", "411069"], "exlcude_cities": []}, "groups_name": "Akshay - West"},{"id": 239, "group_data": {"cities": [], "states": [], "pincode": ["844124", "847236", "813113", "824237", "802129", "803111", "803302", "848122", "803213", "813212", "841206", "848211", "802311", "813205", "853201", "852111", "800006", "800025", "845422", "800019", "824115", "802209", "847204", "851128", "843330", "813213", "843104", "844125", "841439", "803107", "821311", "801503", "801304", "804420", "844127", "854303", "821101", "844502", "854109", "845428", "854116", "821112", "841237", "802158", "843327", "803121", "841443", "842002", "841223", "802132", "852202", "803110", "803119", "847410", "841305", "841212", "845406", "848207", "847233", "804422", "821111", "845449", "813105", "845416", "841434", "843109", "844508", "841219", "851211", "803108", "813104", "804434", "845427", "802112", "848506", "844120", "851115", "844121", "821308", "851129", "844118", "844112", "847302", "847306", "800010", "804423", "847232", "805126", "854328", "800023", "843107", "844122", "841504", "824101", "843121", "802215", "843111", "852114", "841227", "847211", "847101", "847402", "805108", "847235", "847214", "821302", "802136", "841417", "841232", "843318", "800015", "845457", "800017", "847223", "802119", "800020", "803307", "846007", "847107", "845414", "805112", "804451", "847201", "802218", "848209", "800003", "845418", "802111", "802212", "811102", "811202", "851130", "805121", "854115", "847304", "851117", "843302", "845104", "800011", "841239", "846001", "811211", "847240", "802204", "824232", "802127", "845302", "813221", "802118", "845105", "846004", "843332", "841413", "843334", "812005", "852214", "845424", "845107", "845304", "802221", "851126", "804454", "811312", "800012", "847428", "803303", "852110", "805102", "804428", "804418", "854114", "843328", "841235", "841403", "813107", "845452", "847427", "841226", "802133", "843331", "847303", "823311", "852101", "841244", "812007", "802183", "802313", "851204", "802101", "848210", "843143", "804452", "821301", "841419", "841409", "852217", "800013", "847407", "824207", "805111", "851127", "847422", "801113", "821115", "851213", "802135", "841426", "852213", "848114", "802165", "845301", "802217", "854337", "843110", "801108", "802115", "800029", "824143", "841221", "847408", "841509", "843126", "845106", "821108", "804402", "802301", "852107", "802220", "847403", "841412", "852125", "803201", "803215", "824111", "805106", "852116", "848505", "812002", "802114", "805107", "841245", "845451", "821113", "821309", "841422", "804419", "845306", "801105", "802126", "845401", "805132", "846005", "805122", "843322", "821312", "847401", "848205", "804406", "844504", "848504", "845417", "824233", "854205", "805141", "847108", "823002", "847308", "805124", "853205", "841215", "847234", "841218", "843112", "848202", "854336", "843311", "843103", "854106", "841435", "811112", "802162", "841222", "845434", "848115", "842003", "845432", "847305", "848203", "845431", "852108", "802163", "845440", "844123", "854113", "847423", "824216", "851201", "814131", "813209", "843301", "847106", "843326", "845413", "854326", "845454", "800009", "824220", "848134", "854332", "841205", "841501", "843101", "841208", "811101", "821107", "843319", "854339", "841423", "841301", "844505", "848201", "802214", "846009", "845307", "841428", "804408", "855115", "805101", "821109", "847104", "802201", "851131", "824231", "802211", "847337", "845305", "847225", "824219", "843324", "824113", "843108", "845425", "811301", "802152", "802316", "811315", "804405", "848160", "843128", "851116", "852126", "801501", "824206", "841508", "841242", "852139", "811304", "802116", "848121", "811313", "847109", "800007", "824129", "852124", "801302", "805125", "802154", "811316", "843113", "800026", "811213", "800008", "855102", "852123", "848501", "847102", "854317", "803117", "842004", "801102", "844119", "813109", "823001", "852112", "841238", "802157", "843117", "811309", "844126", "845459", "847224", "852128", "843320", "841507", "841213", "812001", "852161", "812004", "851217", "843125", "844503", "854101", "802203", "813102", "813110", "800001", "845303", "841460", "802302", "811305", "805123", "802161", "841436", "847452", "802156", "812006", "824235", "841224", "854305", "853204", "805109", "841313", "843124", "847212", "802120", "852121", "847226", "847122", "847307", "851214", "821104", "802128", "800018", "824118", "824210", "802166", "804401", "848503", "813108", "847301", "851202", "841405", "846006", "845437", "851203", "824112", "846003", "805131", "824301", "841234", "844101", "841240", "851118", "800022", "843114", "852212", "803202", "845415", "845458", "843120", "824122", "845433", "848502", "811311", "845450", "854105", "843329", "802134", "800027", "824124", "841414", "803120", "802117", "854327", "845456", "854302", "801306", "854306", "841506", "855106", "851101", "841203", "844102", "824202", "841316", "843118", "800024", "801303", "841243", "841210", "841216", "843312", "847451", "854329", "854311", "803203", "854331", "803113", "802164", "803211", "801301", "841204", "854338", "805135", "851112", "845315", "845103", "803212", "802131", "841302", "802206", "854108", "844506", "804424", "841217", "843325", "804407", "841214", "803115", "824303", "848130", "852215", "800004", "844111", "848216", "854204", "803101", "852109", "848113", "804453", "824217", "841202", "851135", "804421", "841231", "801505", "851132", "802222", "843313", "851212", "821105", "852127", "844501", "824201", "854104", "821305", "811310", "854202", "854201", "845455", "804430", "848127", "844117", "851215", "847238", "851218", "841427", "852105", "813106", "841201", "802216", "845438", "841411", "844507", "841406", "855101", "845101", "852113", "823005", "803221", "841233", "805110", "843105", "805105", "848206", "852216", "852122", "848236", "852115", "843130", "841225", "854107", "811107", "813210", "804427", "843106", "803109", "805129", "813206", "847123", "804417", "824236", "811214", "845420", "841209", "801109", "841505", "802102", "847115", "801507", "854203", "844115", "848132", "841440", "852220", "841211", "824114", "854315", "855105", "848131", "824102", "824127", "845453", "852132", "811201", "841407", "851111", "847121", "801110", "805104", "824211", "813204", "848302", "854334", "841401", "841416", "851210", "855117", "853202", "843123", "847231", "803301", "802208", "845419", "823004", "802113", "824203", "821110", "851120", "847421", "854325", "800021", "852106", "851114", "851216", "841101", "841425", "841236", "824123", "813211", "802219", "851205", "845102", "844128", "801112", "812003", "847239", "847405", "824121", "811308", "803114", "847222", "801103", "803118", "843351", "804425", "823003", "843317", "842005", "804429", "854117", "848204", "847103", "805236", "843316", "844103", "852131", "821310", "852201", "821106", "848133", "821307", "843127", "854340", "854103", "804404", "841446", "813214", "805128", "852218", "841287", "811302", "821306", "843129", "802210", "843102", "824221", "804432", "841220", "800002", "848301", "841410", "841241", "800016", "824125", "855114", "846008", "844114", "848129", "841420", "841424", "848102", "803214", "855108", "804435", "811314", "804403", "847411", "844116", "845423", "811104", "851133", "854316", "821304", "844113", "845435", "843146", "811212", "845411", "841503", "801506", "824116", "841438", "802125", "801305", "843315", "843333", "845430", "821303", "802123", "854304", "855113", "824234", "843321", "845429", "847409", "802130", "843360", "854112", "853203", "813103", "803116", "845436", "805133", "841402", "843323", "821103", "804426", "802312", "813222", "854312", "802207", "813101", "801104", "802226", "845426", "813203", "805103", "851134", "801111", "841408", "841415", "801307", "854301", "852137", "848125", "843115", "802352", "847227", "824208", "854335", "847105", "824103", "841502", "847404", "811103", "854330", "802155", "802159", "854318", "841441", "813207", "845412", "811106", "847429", "843122", "805130", "846002", "811303", "847229", "855116", "841404", "805127", "848117", "843119", "852138", "841286", "847424", "841442", "800005", "802213", "821102", "847213", "847228", "852219", "841437", "848101", "800028", "852129", "802351", "824205", "802205", "841421", "841418", "802160", "843314", "854102", "844509", "852221", "847215", "802223", "847230", "855107", "802103", "803216", "800014", "802122", "813201", "847203", "848213", "802314", "811317", "841207", "824302", "852130", "811307", "854333", "824120", "841311", "803306", "813202", "824209", "841312", "851113", "848208", "802202", "811105", "842001", "212301", "211001", "211005", "221508", "211010", "212208", "221507", "211008", "212105", "212303", "221505", "212402", "212306", "212109", "212302", "211019", "211012", "212106", "211006", "212305", "229412", "211017", "212405", "221502", "212204", "212507", "211015", "211018", "211013", "212107", "212212", "211016", "229411", "212401", "212108", "211009", "212307", "221503", "212104", "211007", "211002", "212503", "211003", "212502", "212404", "211014", "212111", "211004", "229413", "133005", "133202", "133006", "134003", "134005", "133205", "134201", "133104", "133001", "133201", "133102", "133004", "134007", "134203", "133203", "134011", "133101", "133207", "224146", "224176", "224190", "224202", "224203", "224151", "224232", "224205", "224234", "224171", "224284", "224132", "224143", "224228", "224145", "224230", "224235", "224172", "224149", "224139", "224152", "224129", "224181", "224201", "224125", "224157", "224122", "224231", "224159", "224127", "224147", "224227", "224168", "224137", "224210", "224186", "224238", "224155", "224133", "224183", "224195", "206247", "206252", "206248", "206241", "206121", "206255", "206129", "206246", "206122", "206249", "206250", "206244", "206251", "206243", "276288", "275307", "223225", "276404", "276131", "223227", "223221", "276306", "276139", "276123", "223224", "223223", "276204", "276303", "276202", "276128", "276121", "276135", "276138", "276001", "276206", "276137", "276141", "276302", "276208", "276129", "223226", "276207", "276122", "223222", "276127", "276305", "276126", "276143", "276124", "276304", "276301", "276140", "276203", "276142", "276125", "276406", "276136", "276201", "276205", "271825", "271824", "271865", "271851", "271901", "271804", "271903", "271871", "177220", "172102", "176310", "171203", "172001", "177042", "175124", "173205", "171211", "173032", "176054", "174507", "176202", "173024", "177207", "177029", "173222", "172022", "177044", "177110", "176033", "172030", "173223", "174321", "175023", "176316", "175101", "176318", "176064", "172103", "176027", "176031", "174031", "172118", "177114", "176208", "175047", "176095", "176001", "172113", "176051", "175007", "173025", "176115", "171213", "175037", "176085", "174017", "176065", "174023", "174319", "176041", "174311", "175028", "176057", "173212", "173213", "171007", "176040", "177007", "175016", "175129", "173233", "177202", "173229", "175105", "176081", "176200", "176209", "176048", "174307", "176323", "175130", "175042", "171018", "172112", "175139", "174101", "176402", "175126", "171103", "177034", "176319", "171219", "171004", "173028", "175051", "171008", "176077", "174026", "175039", "175038", "176044", "172034", "176098", "176038", "176071", "175005", "177208", "171301", "174012", "175008", "175133", "176315", "174303", "176042", "174320", "176036", "171216", "177201", "174002", "174201", "176501", "175026", "176026", "176211", "176110", "176101", "174034", "177038", "176075", "172024", "176088", "175001", "176097", "177204", "177045", "175033", "175103", "175034", "174317", "175010", "177023", "173220", "173029", "177119", "176206", "176063", "173234", "174036", "176058", "176325", "171215", "176207", "172031", "175035", "176037", "171208", "175036", "172114", "172116", "176073", "176303", "174103", "177601", "173221", "175019", "175002", "171206", "177118", "177501", "175029", "176213", "177301", "177024", "175017", "175138", "177219", "172106", "176060", "172029", "173201", "176308", "174021", "172110", "177212", "176066", "173218", "171102", "173204", "177103", "177033", "173214", "174011", "173021", "171204", "177112", "176502", "171005", "174306", "176023", "177108", "171001", "177040", "176102", "175106", "171217", "176403", "177048", "176215", "173210", "174004", "175125", "172101", "174027", "176312", "176210", "173026", "173208", "176083", "175143", "174503", "175049", "176090", "176049", "174308", "171012", "174029", "173202", "176061", "175024", "176302", "172021", "176022", "176320", "177106", "176045", "173295", "174302", "171209", "173030", "175102", "177001", "176092", "176301", "176201", "172028", "175004", "176053", "172111", "172104", "175003", "175122", "173215", "177105", "174312", "176052", "175009", "172032", "174033", "176225", "175200", "174309", "176216", "177117", "177107", "176029", "174035", "174013", "175128", "176214", "174015", "177210", "171010", "177211", "173209", "177027", "176076", "173027", "176103", "175046", "171220", "176096", "171224", "176021", "176059", "176039", "171222", "176125", "171212", "177101", "176601", "176203", "176401", "176086", "176313", "177205", "176030", "176311", "174001", "176093", "174005", "176091", "172201", "171226", "172107", "175142", "175132", "174310", "176218", "175030", "175014", "172025", "173104", "174032", "172023", "177113", "177109", "177206", "176089", "175134", "175104", "176128", "175031", "173031", "175136", "171214", "175050", "177022", "172027", "171201", "174315", "175027", "171218", "174305", "175123", "176082", "175018", "175141", "176317", "172117", "174028", "177041", "172109", "171202", "173230", "176219", "177025", "172026", "176043", "175052", "177213", "176309", "177039", "174301", "176321", "171009", "171002", "172108", "176109", "177021", "173211", "175025", "173101", "173235", "171013", "176204", "176047", "174024", "177028", "176087", "174314", "171019", "174102", "171210", "177401", "176205", "177020", "173022", "175032", "175012", "171225", "177006", "174003", "176062", "174030", "171221", "177209", "173023", "171006", "175021", "174505", "176324", "171011", "177104", "175048", "176025", "176108", "175131", "176094", "177031", "176055", "175011", "174316", "172115", "171207", "173225", "176056", "176306", "173207", "173206", "171223", "175140", "176111", "171003", "176305", "175121", "175006", "176217", "175040", "172033", "176304", "174405", "177043", "177005", "172002", "173217", "177026", "177111", "176084", "171205", "176028", "173001", "176107", "177203", "174304", "175015", "175013", "176032", "172105", "176314", "192305", "180001", "182148", "191101", "193103", "180019", "182144", "194109", "193101", "192233", "190010", "181101", "190006", "192232", "191102", "192125", "182202", "181205", "181152", "192123", "190014", "190008", "193411", "191112", "182126", "182101", "181207", "190002", "191103", "182312", "182121", "194201", "185202", "184201", "181203", "185156", "182311", "191201", "180011", "193223", "194403", "192126", "184204", "192301", "185135", "180013", "193301", "185155", "181102", "193303", "182313", "190001", "193503", "193403", "193502", "181145", "190023", "190020", "182127", "181143", "192101", "194404", "191202", "192124", "191131", "184120", "185152", "192221", "192121", "192303", "180015", "181206", "192211", "192306", "193201", "193224", "190007", "192304", "181202", "180018", "184141", "185101", "192202", "180012", "182205", "180006", "181132", "182146", "193401", "184142", "192230", "180007", "182206", "190017", "194102", "182161", "182104", "193123", "185102", "184202", "190005", "181133", "192401", "185132", "181122", "194104", "192212", "184121", "182315", "192129", "191121", "182222", "184145", "190003", "182147", "182221", "192201", "193108", "180016", "185233", "194401", "181131", "182124", "184148", "184152", "184205", "194103", "181224", "180010", "184101", "184102", "191113", "191111", "184151", "190012", "190019", "182204", "193302", "190021", "193222", "193121", "194101", "185201", "192302", "180003", "190015", "182141", "192210", "190011", "182203", "190018", "180009", "194105", "181111", "185154", "180004", "182142", "182122", "193402", "182320", "185212", "190009", "185153", "184203", "192122", "184206", "181121", "191203", "190025", "185203", "182128", "181221", "181141", "194302", "184144", "191132", "182201", "190004", "184143", "182143", "185151", "182125", "194402", "193221", "182301", "193202", "181204", "180002", "194106", "182145", "180017", "181124", "185211", "185234", "192231", "193501", "181201", "193404", "193122", "185121", "181123", "180020", "184104", "194301", "185131", "180005", "193225", "193504", "190024", "828132", "828204", "835209", "816118", "835227", "829128", "822121", "814147", "833222", "825317", "829201", "829105", "833105", "835212", "814142", "814167", "833202", "825406", "831007", "825407", "829114", "814103", "828205", "834005", "825329", "822120", "832109", "831001", "827004", "833106", "835203", "828308", "825106", "829209", "814115", "829111", "825167", "831004", "822114", "833213", "816110", "815311", "829107", "832103", "829132", "814144", "825413", "814102", "815317", "829113", "835228", "825336", "829112", "835303", "829301", "816102", "833204", "829121", "832104", "822101", "828305", "814166", "835202", "833212", "829131", "814141", "835231", "815301", "825324", "828104", "833217", "822131", "815318", "831010", "835235", "814101", "814133", "828206", "829207", "828122", "833215", "828303", "827006", "814156", "835214", "814145", "815351", "833220", "816106", "829123", "835234", "835205", "829150", "831015", "835230", "825421", "825403", "829210", "828207", "832102", "828107", "829126", "829103", "826001", "828404", "816104", "816107", "832108", "815312", "825103", "825411", "831020", "825102", "822117", "835210", "814157", "828131", "828129", "814153", "815357", "827001", "822133", "832304", "813208", "833102", "834002", "831012", "815302", "828116", "814160", "815316", "829119", "829205", "828134", "822123", "829104", "815314", "829108", "822119", "833101", "825409", "825323", "834008", "827013", "835220", "829208", "816115", "825132", "825320", "832401", "828109", "825303", "835325", "828115", "825326", "835211", "828135", "828124", "828304", "828201", "829133", "822116", "814151", "828202", "832302", "829118", "832107", "832105", "828403", "816105", "822126", "829144", "831011", "814158", "829109", "822113", "835222", "814143", "814154", "827003", "816109", "815353", "815355", "832106", "825401", "822118", "828127", "827014", "831009", "831016", "829143", "835102", "832404", "829130", "828306", "828117", "835206", "825108", "825311", "815352", "814114", "828111", "835101", "822115", "825402", "828112", "833103", "828110", "835217", "828101", "835103", "814165", "835229", "825301", "816117", "829125", "822111", "822132", "831005", "827012", "825322", "834009", "833219", "831006", "828120", "825315", "825405", "833223", "835232", "814116", "827009", "828105", "833221", "829129", "834011", "826004", "814112", "829117", "831013", "832101", "828302", "835218", "832303", "828114", "835207", "829127", "828203", "826005", "828307", "822128", "822110", "835225", "829122", "832301", "828301", "822102", "825109", "816108", "825314", "835301", "828309", "814119", "833214", "829206", "828401", "825101", "828123", "835324", "835215", "822112", "828106", "833201", "834006", "815313", "822122", "825418", "815315", "831014", "828119", "834003", "835216", "826003", "825330", "814152", "828108", "829101", "828133", "831018", "832112", "814110", "829116", "835208", "835226", "814111", "825408", "828128", "827010", "831017", "825316", "829202", "835221", "834001", "828125", "828130", "831019", "816103", "833203", "828402", "834004", "829102", "816116", "825412", "828113", "825321", "814149", "814122", "828126", "816129", "835213", "832403", "816120", "828121", "827302", "834010", "832111", "825318", "831003", "825319", "828103", "832402", "814146", "827011", "825313", "822129", "835233", "833104", "825325", "814148", "835201", "822124", "814113", "825404", "814120", "815359", "822134", "829204", "825302", "831002", "833216", "825410", "833218", "822125", "835219", "829203", "814155", "816101", "835204", "814118", "832110", "825312", "829135", "815354", "829134", "835223", "829149", "814150", "829106", "829110", "835302", "761012", "752055", "756083", "761121", "758002", "768119", "770052", "760002", "755062", "769042", "769010", "753009", "768039", "757003", "770040", "756086", "752111", "765033", "752016", "752061", "767065", "756040", "751004", "752104", "752100", "761018", "754107", "761210", "756124", "764045", "752026", "756048", "769001", "751009", "761052", "754240", "755024", "754037", "769014", "754296", "765017", "761108", "768019", "756118", "760004", "759013", "757050", "770011", "754289", "768049", "767061", "770024", "761011", "757033", "754010", "751012", "754133", "761141", "756059", "759105", "754111", "765024", "759123", "768108", "757020", "756114", "768001", "762106", "765018", "764077", "757018", "770031", "754209", "752035", "761037", "752064", "761122", "766101", "756037", "754223", "764036", "752078", "756034", "751018", "754082", "753008", "756079", "757019", "767029", "759104", "759021", "761002", "752092", "763003", "761042", "761209", "754213", "755018", "756056", "757102", "754106", "752106", "768204", "768234", "756058", "758047", "767028", "769043", "762107", "757040", "754140", "752062", "756171", "756130", "768225", "756100", "756116", "752014", "752070", "751019", "756115", "761100", "754216", "768203", "769016", "759037", "766001", "758085", "759028", "761140", "754023", "752069", "754139", "768212", "757039", "770017", "770023", "751014", "763001", "761104", "761207", "757024", "751022", "761015", "757029", "752030", "761131", "768020", "759040", "756084", "757043", "763004", "754210", "754013", "756019", "768002", "754239", "754224", "751008", "758079", "760005", "767023", "768105", "755020", "758043", "757086", "754214", "768109", "761206", "753004", "762020", "754250", "761107", "754231", "761032", "767039", "761117", "752105", "764021", "768222", "764052", "758041", "758016", "766012", "761120", "752015", "766002", "764057", "767060", "755011", "762109", "757026", "759129", "752077", "752113", "752019", "764006", "757016", "757052", "759128", "768115", "754153", "764062", "764088", "768111", "757051", "752034", "767066", "761146", "758030", "761029", "769015", "755026", "756139", "754227", "753007", "754135", "751015", "758025", "754003", "754100", "760008", "767019", "757001", "755017", "756137", "760010", "755049", "762100", "761125", "754027", "752038", "764038", "753003", "754110", "755001", "760006", "769003", "757092", "754290", "753012", "752110", "761003", "766111", "758045", "759026", "752011", "762027", "752094", "757002", "752023", "761020", "761124", "764071", "751005", "757079", "764056", "759125", "753014", "761041", "758023", "759148", "762012", "758021", "765013", "754112", "765034", "757103", "755006", "770048", "767030", "757017", "751010", "768050", "757049", "762028", "756111", "766103", "757034", "754134", "761115", "770076", "755003", "757038", "761043", "770012", "756081", "766026", "756138", "757028", "765020", "757030", "752091", "756002", "761013", "757100", "761114", "754136", "757048", "756021", "754219", "757021", "757074", "764048", "752001", "754114", "754138", "752119", "768052", "754002", "769011", "768233", "759014", "761045", "758017", "752081", "762010", "758029", "751002", "756041", "751024", "757055", "761109", "767033", "767020", "758036", "752068", "765002", "766031", "767025", "751007", "762112", "755050", "754245", "754293", "753001", "756039", "758076", "756049", "767067", "756027", "761006", "761201", "755023", "759141", "765029", "752118", "757104", "752024", "752114", "767001", "754028", "754025", "756113", "768018", "754031", "764043", "754208", "754200", "766014", "762016", "762022", "761035", "759130", "761105", "768027", "752060", "768038", "755061", "756026", "759122", "756047", "762026", "761017", "758080", "770001", "752045", "757046", "754011", "752018", "751023", "764049", "754217", "751016", "769004", "756001", "762018", "768035", "765016", "757035", "754026", "754143", "759025", "756126", "754207", "758040", "758084", "752013", "757093", "752003", "770033", "767017", "762105", "754001", "768217", "766013", "754142", "766016", "770044", "754007", "754145", "756144", "770046", "759001", "754228", "758019", "756133", "768005", "752046", "770035", "770016", "767045", "764039", "767068", "764027", "760001", "758013", "767035", "761031", "768202", "761132", "752116", "764063", "752084", "754215", "768121", "767021", "768224", "757025", "755007", "761214", "756033", "758014", "766110", "757101", "768221", "755022", "752101", "752027", "759147", "752102", "768028", "768037", "766027", "762030", "756135", "768031", "764011", "770019", "767070", "765022", "754024", "756051", "759126", "752093", "764047", "758081", "768215", "755009", "752037", "764072", "756131", "758046", "759120", "754244", "764059", "756167", "756035", "768211", "757023", "765026", "766107", "770074", "754005", "768104", "756181", "756003", "766023", "761101", "752021", "761004", "766011", "754292", "770013", "768228", "768201", "754141", "754253", "754160", "752079", "752050", "756128", "752020", "754022", "757027", "755012", "767041", "768226", "759039", "770015", "759127", "768032", "756122", "757075", "756025", "767024", "766015", "766106", "767016", "756168", "756165", "759022", "752120", "765001", "768045", "767048", "764001", "768107", "761213", "761027", "764028", "770018", "768004", "752057", "768106", "764046", "754218", "764002", "754295", "755005", "759102", "761144", "753015", "756125", "754008", "768033", "762029", "769006", "764087", "759124", "770037", "762021", "757105", "754030", "764040", "761110", "759117", "757084", "761211", "755010", "766036", "758031", "756162", "759106", "756182", "767042", "766105", "757082", "761215", "767002", "762013", "756164", "761111", "766108", "767027", "757031", "768218", "754012", "752022", "751030", "754211", "761217", "752085", "766102", "759100", "762103", "770043", "754246", "751013", "768213", "769012", "768103", "757037", "759020", "754203", "761010", "751003", "761009", "755027", "754137", "767022", "756123", "761212", "752063", "752054", "754130", "756044", "756038", "757106", "754035", "759016", "755044", "764073", "768219", "762015", "757047", "755025", "761016", "755013", "757081", "754225", "764085", "754032", "754212", "764070", "753010", "754221", "756080", "762019", "761007", "758027", "752066", "754103", "756023", "767018", "759024", "757041", "764003", "754162", "770021", "762104", "756120", "764042", "756134", "759015", "764014", "754071", "757014", "759023", "753002", "756119", "757036", "755043", "764075", "766037", "756042", "764074", "757087", "764086", "754222", "761005", "754029", "751011", "761116", "761151", "755014", "757083", "761208", "762011", "756036", "757077", "761014", "767032", "756024", "767046", "753011", "754201", "752089", "752080", "758015", "756032", "761028", "761126", "754248", "761019", "752108", "751017", "755004", "756166", "759146", "758001", "759121", "765023", "759132", "768048", "758028", "754006", "762110", "762024", "759027", "762102", "756117", "766118", "754294", "760003", "755016", "766104", "760009", "761119", "758038", "766017", "754119", "764005", "754108", "759143", "752115", "751001", "770041", "754009", "754113", "762101", "757091", "758018", "754102", "755036", "766019", "767026", "757107", "768113", "764004", "755019", "766020", "768026", "755015", "754004", "757042", "752002", "757022", "758083", "761200", "751025", "754018", "761143", "768214", "767038", "768118", "768003", "756112", "768200", "767062", "754105", "752083", "768017", "764078", "759103", "756085", "752090", "758032", "758044", "755008", "770075", "752109", "754159", "754205", "763008", "752065", "768216", "768029", "752056", "770042", "768030", "764076", "764044", "759111", "759017", "762001", "759019", "754132", "754109", "758037", "759116", "764058", "764041", "753006", "758078", "759118", "758034", "761055", "768016", "752017", "756029", "766018", "770034", "770020", "770039", "759145", "761133", "756043", "765021", "752107", "756129", "770051", "765019", "756030", "764081", "767040", "764055", "752082", "761123", "761008", "764061", "770032", "764037", "756055", "756060", "758026", "756020", "757085", "762017", "758082", "752121", "770070", "756046", "756022", "768227", "762002", "754104", "762014", "761054", "756121", "770073", "764051", "754120", "752012", "768040", "769005", "751006", "761025", "756028", "761102", "768036", "758022", "754021", "752031", "767037", "764020", "756132", "769008", "766029", "770038", "758035", "770002", "768025", "756045", "756163", "756127", "768042", "768034", "757045", "766100", "768006", "768220", "769002", "770036", "765015", "758020", "761106", "761030", "769009", "769007", "766032", "768110", "761026", "769013", "759119", "754220", "759101", "760007", "768102", "752025", "770072", "755028", "751021", "756101", "761001", "757073", "754131", "761103", "754204", "759107", "763002", "752103", "761118", "765025", "768112", "757032", "757054", "770022", "759018", "754202", "753013", "754206", "762023", "757053", "751020", "766028", "770014", "141801", "144418", "144025", "140055", "151201", "142044", "141105", "143409", "144031", "146021", "144630", "141106", "152118", "140101", "142055", "146107", "148025", "143604", "143301", "144511", "140126", "144506", "144521", "145027", "144517", "144009", "140306", "141126", "140701", "144402", "141412", "142034", "144403", "144208", "142050", "143606", "148102", "144522", "151105", "142049", "143116", "144023", "141104", "152115", "152116", "148019", "143513", "140407", "141118", "146108", "151102", "151203", "151209", "160055", "151211", "140417", "140601", "144528", "141122", "144421", "141012", "145022", "143008", "144011", "146024", "152113", "151501", "141007", "146113", "152032", "143205", "144026", "152121", "141108", "140124", "148022", "160062", "151002", "143112", "143117", "144512", "144027", "141416", "141127", "144041", "148100", "141103", "144806", "146101", "142039", "146111", "140602", "144524", "152003", "151206", "140308", "141422", "146102", "144006", "144033", "141201", "144201", "141413", "144509", "142029", "142025", "143406", "143501", "143114", "141002", "144523", "151506", "144419", "140102", "144209", "152023", "152122", "144405", "143603", "148001", "146106", "141001", "152123", "143506", "143512", "144406", "143149", "144422", "144039", "144035", "142002", "151510", "144620", "141008", "143419", "141411", "140405", "140118", "141119", "144703", "152001", "143605", "142043", "143302", "140901", "141415", "140604", "147002", "147111", "141414", "144632", "143601", "144202", "144621", "142046", "144628", "144529", "146105", "140507", "141203", "143303", "143526", "144207", "141116", "144210", "144520", "151004", "142026", "147105", "144527", "142057", "143534", "151302", "148017", "144028", "143201", "144410", "151205", "144701", "143115", "165001", "144002", "144802", "151401", "140412", "143402", "148026", "144525", "143530", "140501", "148109", "144223", "144024", "151508", "160071", "144504", "141101", "148033", "144507", "141110", "147005", "151504", "143519", "144801", "143511", "144206", "140702", "146115", "142040", "152005", "143520", "143517", "144416", "144012", "147003", "144526", "152128", "143525", "148101", "151210", "141421", "142035", "143102", "146104", "160070", "140114", "151207", "147004", "143529", "142024", "140108", "142031", "142021", "144530", "143514", "143401", "140301", "151301", "144702", "144501", "144623", "142053", "160061", "144411", "151213", "143415", "151101", "148030", "140103", "148031", "148105", "142048", "145025", "142058", "144022", "152022", "144409", "148108", "148034", "144005", "141016", "141205", "143410", "144104", "147202", "142047", "144044", "142003", "141003", "141004", "143107", "142001", "144415", "148103", "144101", "143108", "143006", "143533", "144042", "151204", "148021", "143518", "143001", "143118", "144103", "144401", "140133", "144803", "144040", "142030", "140401", "143412", "140406", "143203", "143416", "145001", "141015", "147203", "145023", "151104", "152021", "144404", "142023", "144625", "151507", "152026", "144819", "141418", "143408", "143528", "144502", "144624", "144214", "144533", "151212", "142028", "140123", "143411", "143005", "147021", "144805", "141113", "144311", "141121", "147101", "152114", "152107", "148020", "152031", "141109", "140413", "152033", "145029", "143111", "152028", "144519", "141013", "143527", "143202", "143305", "143515", "152112", "140802", "143002", "144514", "151208", "151505", "160104", "141010", "144010", "144221", "143502", "142033", "141206", "142054", "141125", "147201", "151108", "144043", "152004", "144513", "144030", "143103", "144211", "143505", "144007", "144626", "144014", "148002", "152124", "143516", "143414", "142037", "144003", "144004", "144032", "143422", "144302", "140111", "160103", "144516", "146110", "141014", "151001", "142041", "151005", "152117", "147301", "146001", "141120", "144531", "143119", "144105", "144029", "140307", "151502", "144606", "144603", "144037", "141117", "141006", "147006", "145026", "143504", "142052", "144301", "146116", "142042", "144222", "144532", "140001", "141419", "144213", "148107", "152101", "142056", "146023", "144306", "140117", "144008", "152024", "144001", "144629", "144102", "141202", "147007", "142011", "140506", "144034", "146103", "144510", "147104", "143407", "151509", "143413", "142027", "141115", "142060", "142045", "140201", "144305", "151111", "144518", "143507", "143204", "140113", "143009", "144212", "148104", "146022", "144505", "144417", "148023", "144407", "148024", "147102", "146109", "141102", "140110", "141107", "151103", "144804", "146112", "144515", "140109", "148027", "151106", "144508", "160014", "144602", "152025", "148106", "144303", "144633", "140116", "144036", "144020", "145024", "143531", "148029", "143113", "143022", "141123", "144224", "140402", "144408", "143521", "143105", "140112", "144601", "143101", "148035", "141017", "144205", "160059", "140603", "147103", "141114", "143109", "147001", "143304", "141417", "143532", "142032", "143602", "152132", "144106", "148028", "144204", "144021", "141204", "148018", "142036", "140115", "144622", "146114", "140107", "142022", "141112", "145101", "144631", "151503", "142038", "144503", "152002", "144216", "141401", "151202", "151003", "342605", "306302", "326001", "333515", "332715", "322236", "333032", "312027", "303110", "331501", "331031", "303704", "303904", "305007", "331803", "314025", "321613", "331802", "345026", "332312", "313325", "323601", "303601", "333707", "321022", "341502", "313601", "305206", "307043", "322255", "311803", "332710", "326038", "322230", "322026", "303007", "335804", "341305", "323021", "313038", "333503", "344034", "313323", "344501", "305408", "301404", "313031", "305001", "333307", "325218", "344032", "305921", "332305", "331303", "313211", "344708", "314027", "344044", "322704", "341031", "312022", "332711", "312001", "305405", "303005", "341030", "313324", "332302", "301414", "335701", "342602", "301026", "313605", "325001", "304507", "324002", "313702", "341514", "321608", "335901", "334006", "332706", "331305", "323303", "305812", "303302", "313328", "306422", "305024", "305412", "341027", "321302", "334604", "345024", "306305", "342001", "313011", "328023", "322205", "332714", "301714", "322033", "313027", "307512", "328026", "332002", "326519", "335025", "305203", "332411", "327001", "322220", "343023", "314022", "322218", "306705", "322001", "332708", "333012", "305009", "322240", "333504", "331024", "306704", "301023", "324005", "331518", "323802", "303001", "343028", "302001", "323302", "322028", "322251", "335073", "313205", "301406", "314037", "333033", "323305", "301024", "322703", "341507", "343032", "326037", "326529", "302168", "303803", "332721", "326034", "324009", "342011", "312203", "332719", "331701", "334802", "342006", "303505", "331021", "332707", "328027", "334801", "314402", "307510", "328041", "304804", "301604", "341512", "322219", "331507", "312612", "332718", "333516", "306707", "306304", "335504", "341024", "333011", "341501", "342801", "331026", "327026", "344035", "306504", "334302", "311001", "327034", "333053", "302020", "341317", "303303", "306306", "323023", "301707", "314403", "304021", "322211", "332318", "341306", "333704", "334804", "314801", "341028", "321001", "303109", "327031", "334022", "331811", "303603", "301411", "344706", "303107", "306021", "307511", "313202", "332301", "313322", "306119", "342309", "301701", "313603", "342022", "343029", "331502", "328030", "332401", "331403", "342023", "325208", "342901", "333036", "302018", "306308", "312606", "332716", "305025", "306902", "301001", "328025", "311202", "313015", "313206", "325004", "301018", "313341", "302012", "313207", "301706", "305201", "335002", "312616", "335512", "342302", "333502", "335503", "303701", "334021", "305005", "303103", "325201", "305622", "345031", "332001", "303106", "332712", "342012", "313902", "313802", "333001", "304503", "313024", "341001", "343042", "301709", "305406", "301705", "307019", "306101", "335038", "333021", "328001", "325204", "312204", "342601", "325221", "343022", "305925", "304502", "311023", "335805", "301027", "303712", "303804", "334601", "334803", "302032", "322202", "325222", "301704", "343040", "303510", "325003", "313321", "334602", "324010", "322254", "301002", "306307", "313329", "332031", "343002", "305819", "322249", "303328", "341542", "305926", "303501", "305415", "311801", "326501", "322234", "303102", "326021", "325219", "311011", "312603", "333022", "332405", "342301", "305026", "322023", "342003", "342311", "326023", "332023", "314001", "311605", "344701", "303105", "335024", "301019", "305924", "305624", "333029", "334201", "313803", "303348", "342312", "301020", "321601", "314023", "332307", "321301", "303801", "305204", "312605", "311401", "312402", "302004", "326036", "332705", "312615", "313706", "301409", "303120", "304801", "321615", "343001", "321025", "302033", "343021", "302036", "306706", "342014", "303508", "311806", "301413", "343049", "333305", "313708", "333024", "314034", "303008", "325205", "341520", "312620", "303903", "304022", "313905", "301025", "306115", "307028", "341504", "306001", "303507", "305404", "323022", "334401", "313301", "301416", "307501", "312024", "306102", "305629", "333030", "342303", "335022", "335705", "302003", "325009", "303325", "303315", "306703", "335001", "304505", "311201", "326039", "326518", "321406", "335525", "313331", "335064", "332702", "323602", "300018", "303503", "303806", "335023", "323307", "306502", "342314", "342307", "325217", "343041", "345001", "307023", "306912", "332041", "335501", "305022", "313602", "303301", "304001", "304501", "305813", "333304", "332709", "321026", "314804", "312206", "341026", "324004", "301030", "333023", "341515", "306501", "344022", "335702", "305630", "325203", "345033", "327605", "321403", "311204", "325223", "342305", "332315", "322204", "305815", "303329", "305623", "334303", "322024", "344025", "331025", "333031", "307001", "344704", "321407", "322702", "322030", "343024", "305814", "324006", "304025", "304023", "313906", "332042", "332030", "312604", "321411", "335523", "334003", "341508", "322216", "344011", "324007", "341319", "303327", "321607", "321610", "303807", "307024", "321606", "331302", "342606", "342902", "307803", "307025", "342304", "334004", "322241", "305922", "341304", "342310", "332026", "311408", "303305", "321402", "322034", "345028", "302039", "307026", "323801", "323616", "321405", "301022", "311805", "306114", "332402", "307513", "321410", "335511", "312401", "303004", "345021", "328024", "332317", "322201", "311203", "332028", "335041", "345034", "321202", "314030", "327603", "341316", "342015", "323613", "314029", "311025", "311402", "327025", "326520", "311407", "344021", "332713", "328022", "303104", "324003", "324001", "342005", "332404", "307801", "303108", "331402", "314401", "322213", "331029", "306503", "341533", "325215", "312602", "311604", "321206", "303122", "341551", "314021", "301412", "322021", "303602", "323615", "341302", "333034", "303323", "326513", "341022", "333041", "306301", "332722", "331001", "307027", "305401", "305207", "303313", "312614", "321408", "326517", "306103", "323306", "341513", "335526", "303502", "342026", "303908", "323025", "333042", "322029", "321602", "322252", "344801", "341505", "332025", "332403", "341518", "325207", "313203", "332021", "305801", "301402", "332701", "312025", "313604", "334001", "304802", "327801", "326035", "321201", "306709", "302017", "303003", "333702", "306022", "301403", "345023", "332602", "322025", "332311", "342008", "326502", "327602", "326033", "313611", "323803", "313801", "305817", "311404", "302021", "302013", "325601", "343030", "311603", "344702", "305021", "302019", "325206", "342021", "323001", "311804", "302002", "327024", "335524", "335027", "334808", "321204", "306105", "321609", "325220", "326514", "327606", "344024", "332603", "303905", "302031", "326512", "306303", "311403", "331030", "322238", "303706", "331801", "307029", "334202", "326516", "303509", "314404", "302034", "305802", "313330", "331506", "333705", "313327", "303506", "313026", "312404", "303805", "305601", "331301", "335039", "322701", "343048", "312901", "322215", "331503", "310707", "303006", "306901", "313342", "344012", "311301", "302040", "321409", "342037", "305402", "321611", "303326", "327032", "331411", "301035", "325602", "342013", "305625", "321633", "332029", "334603", "306603", "333303", "332316", "345027", "344031", "321401", "307021", "314031", "313001", "302006", "311030", "301712", "332406", "301703", "333027", "342027", "305628", "341303", "302027", "331517", "333308", "303119", "305627", "327021", "312623", "332746", "314026", "341511", "327601", "305012", "304803", "342308", "344043", "344001", "342306", "314035", "335711", "333028", "326515", "321404", "335802", "312601", "312207", "344026", "305205", "313004", "324008", "301427", "321605", "328031", "311026", "325216", "322243", "344037", "341509", "344502", "321612", "342024", "333025", "306421", "305002", "311022", "305003", "328021", "321614", "323603", "314011", "328029", "307031", "321642", "306116", "335707", "302007", "342025", "321023", "314032", "335703", "321021", "312403", "334403", "302005", "301401", "331023", "313903", "327022", "303604", "332742", "301410", "313804", "335065", "301021", "305403", "305023", "341318", "312619", "335040", "305202", "313701", "331022", "334023", "311302", "332703", "305004", "335502", "305923", "321203", "341301", "306401", "341029", "312205", "312021", "341021", "341510", "332024", "333701", "314038", "313703", "314036", "335063", "302015", "313003", "313333", "301405", "301028", "335801", "322214", "325214", "302026", "313901", "321028", "321303", "332303", "302022", "303009", "311021", "323301", "335037", "311601", "305816", "326022", "312201", "341506", "342603", "303121", "305621", "341517", "343025", "305901", "342007", "344027", "303338", "322203", "332304", "341503", "343039", "343027", "305811", "311024", "313334", "331027", "313022", "325202", "322212", "306023", "303504", "311602", "302016", "303901", "303511", "314028", "303906", "312613", "334305", "313705", "333501", "307022", "335803", "312202", "313904", "333801", "301407", "327604", "344703", "307514", "341516", "345022", "345025", "335704", "306601", "313332", "301408", "305927", "305407", "322027", "314024", "302029", "313201", "304024", "333026", "327023", "335061", "302037", "306126", "342802", "333035", "304504", "302028", "313704", "321205", "301415", "328028", "306708", "314406", "335051", "303002", "323304", "313002", "312023", "312622", "303012", "306701", "323024", "326530", "341025", "344705", "306702", "335513", "301708", "307802", "303702", "301702", "303304", "331504", "301713", "312617", "327027", "334402", "321024", "306602", "332027", "342028", "331505", "322242", "333514", "313204", "341519", "335021", "307030", "331028", "341023", "325209", "323614", "307515", "311802", "331304", "335062", "333302", "332601", "342604", "306104", "344033", "271875", "271802", "271882", "271870", "271872", "271904", "271881", "271801", "271830", "271841", "271902", "271855", "277202", "277304", "277207", "221711", "277401", "277219", "277205", "277303", "277301", "277001", "277214", "277506", "277203", "221713", "277123", "277121", "221712", "277502", "221709", "221715", "277208", "277210", "277403", "277213", "277124", "277211", "277201", "277402", "277204", "277503", "277504", "221701", "277302", "221717", "221718", "277216", "221716", "277501", "277209", "271207", "271607", "271206", "271215", "271203", "271609", "271861", "271201", "271604", "271306", "271208", "271209", "271205", "271210", "210123", "210203", "210121", "210129", "210201", "210126", "210001", "210202", "210125", "210120", "210128", "224120", "225306", "225119", "225208", "225304", "224118", "225303", "225301", "225204", "225409", "225125", "225122", "225123", "225403", "225201", "225207", "225404", "225206", "225003", "225121", "225203", "224119", "225412", "225302", "225405", "225120", "225413", "225126", "225401", "225305", "225001", "225416", "225205", "225124", "225202", "225414", "225002", "225415", "243005", "243402", "243506", "243301", "243503", "243001", "243126", "243302", "243303", "243122", "243201", "243407", "243501", "243003", "243006", "243502", "243202", "243002", "243203", "243401", "243504", "243123", "243403", "262406", "243505", "243004", "272191", "272151", "272123", "272127", "272301", "272177", "272125", "272182", "272131", "272002", "272163", "272190", "272175", "272302", "272194", "272171", "272130", "272201", "272124", "272181", "272161", "272001", "272150", "272128", "272155", "127040", "127029", "127310", "127309", "127043", "127306", "127308", "127042", "127022", "127032", "127027", "127028", "127031", "127035", "127021", "127307", "127046", "127030", "127041", "127111", "127045", "127025", "127201", "127114", "127026", "246763", "246762", "246723", "246745", "246734", "246725", "246736", "246746", "246737", "246721", "246727", "246701", "246732", "246764", "246749", "246731", "246761", "246747", "246722", "246728", "246724", "246729", "246726", "246733", "246735", "243601", "243641", "243722", "243726", "243724", "243725", "243631", "243727", "243630", "243720", "243637", "242021", "243751", "243639", "243632", "243634", "243723", "243638", "243635", "243633", "243636", "232120", "221009", "232104", "232108", "232101", "232118", "232105", "232109", "140119", "160012", "160004", "160020", "160030", "160017", "160002", "160036", "160001", "160047", "160101", "160003", "160102", "160018", "160023", "160025", "140125", "160019", "160035", "160022", "160011", "160009", "160015", "210204", "210209", "210208", "210207", "210206", "210205", "274509", "274807", "274806", "274703", "274501", "274508", "274404", "274604", "274201", "274402", "274182", "274405", "274702", "274202", "274808", "274704", "274001", "274205", "274204", "274208", "274601", "274505", "274701", "274408", "274506", "274603", "274502", "274705", "274602", "224209", "224189", "224161", "224182", "224117", "224158", "224208", "224141", "224206", "224121", "224116", "224204", "224225", "224135", "224153", "224229", "224126", "224001", "224123", "224164", "224207", "224188", "209504", "209601", "209749", "209625", "209721", "209743", "209652", "209503", "209505", "209745", "209724", "209621", "209622", "209651", "209602", "209739", "209502", "209501", "125133", "125120", "125048", "125113", "125050", "125053", "125106", "125051", "125052", "125111", "275203", "233230", "233301", "275205", "233306", "233222", "233221", "233233", "233225", "233228", "232333", "233307", "232325", "232328", "233227", "232326", "232336", "233303", "232327", "233231", "275201", "233223", "232329", "233224", "233302", "275204", "233002", "232331", "232339", "232330", "233232", "233304", "233311", "233305", "233229", "233001", "233310", "233300", "232340", "275202", "233226", "232332", "271602", "271002", "271304", "271601", "271312", "271313", "271003", "271129", "271202", "271401", "271319", "271307", "271001", "271310", "271122", "271302", "271305", "271123", "271303", "271309", "271126", "271308", "271311", "271502", "271504", "271125", "271301", "271124", "271503", "271204", "271403", "271603", "271402", "273012", "273403", "273152", "273165", "273401", "273009", "273402", "273405", "273202", "273412", "273203", "273005", "273411", "273002", "273007", "273211", "273010", "273407", "273413", "273306", "273013", "273404", "273212", "273209", "273213", "273017", "273014", "273409", "273408", "273006", "273158", "273016", "273008", "273406", "273003", "273004", "273201", "273001", "273015", "210501", "210502", "210506", "210301", "210505", "210428", "210507", "210504", "210430", "210422", "210431", "210432", "210341", "241301", "241402", "241401", "241303", "241405", "241123", "241406", "241403", "241203", "241304", "241302", "241122", "241201", "241124", "241121", "241001", "241204", "241125", "241404", "241305", "241202", "241126", "241127", "241407", "125042", "125038", "125044", "125006", "125011", "125005", "125047", "125049", "125004", "125033", "125001", "125007", "125121", "125037", "125112", "125039", "285202", "285001", "285205", "285128", "285204", "285129", "285122", "285123", "285124", "285203", "285130", "285125", "285126", "285206", "285127", "285223", "285121", "285201", "222203", "223103", "222148", "222109", "222135", "222165", "222125", "222128", "222170", "222175", "222145", "222132", "222001", "222136", "222180", "222161", "223104", "222105", "222138", "222144", "222002", "222181", "222202", "222101", "222146", "223101", "222162", "222143", "222129", "223102", "223105", "222137", "222142", "222141", "222131", "222201", "222003", "222204", "222133", "222149", "222127", "222139", "284203", "284306", "284419", "284303", "284302", "284305", "284201", "284121", "284128", "284401", "284002", "284205", "284202", "284301", "284120", "284304", "284206", "284127", "284135", "284001", "284204", "284003", "126116", "126113", "126114", "126112", "126111", "126110", "126125", "126101", "126152", "126115", "126102", "244223", "244241", "244235", "244304", "244251", "244222", "244255", "244231", "244501", "244236", "244245", "244242", "244221", "244225", "136117", "136034", "136033", "136044", "136035", "136027", "136026", "136043", "136042", "136021", "136020", "209720", "209726", "209734", "209722", "209738", "209736", "209728", "209733", "209727", "209735", "209723", "209725", "209729", "209747", "209731", "209732", "209301", "209302", "209204", "209206", "209209", "209310", "209208", "209125", "209311", "209303", "209306", "209101", "209312", "209210", "209111", "209307", "209115", "209308", "209121", "209205", "209112", "209202", "208026", "208012", "208007", "208023", "208001", "208021", "208002", "208014", "208024", "208022", "208019", "208027", "209304", "208006", "209402", "208017", "208008", "208003", "209217", "208009", "208020", "208011", "209203", "208005", "209305", "208004", "209214", "209401", "208015", "208016", "208025", "208013", "208010", "132114", "132037", "132116", "132001", "132046", "132039", "132024", "132054", "132040", "132041", "132022", "132036", "132117", "132023", "132157", "211011", "212205", "212201", "212217", "212213", "212207", "212216", "212218", "212206", "212203", "212214", "212202", "262902", "262701", "262905", "262725", "261502", "262727", "262726", "262803", "262804", "262903", "261506", "262906", "262721", "262907", "262722", "261501", "262904", "262728", "262805", "262723", "262702", "262908", "262802", "262724", "262901", "261505", "262801", "136136", "136129", "136132", "136128", "136038", "136118", "136119", "136130", "136030", "136135", "136156", "136131", "274407", "274203", "274302", "274206", "274409", "274304", "274207", "274801", "274301", "274406", "274401", "274306", "274802", "274403", "274303", "274149", "274305", "284125", "284403", "284122", "284124", "284126", "284501", "284123", "284405", "284406", "284402", "284136", "284404", "226202", "226021", "226029", "226018", "226016", "226101", "226023", "226027", "226008", "226028", "226102", "226103", "226022", "226004", "226501", "226001", "226014", "226104", "226009", "226006", "226005", "226301", "226302", "226026", "226020", "226401", "226015", "226002", "226013", "226007", "226303", "226011", "226203", "226030", "226019", "226025", "226010", "226201", "226031", "226012", "226024", "226017", "226003", "273305", "273162", "273155", "273151", "273163", "273157", "273302", "273311", "273308", "273207", "273303", "273309", "273310", "273304", "273164", "273301", "273161", "210421", "210425", "210433", "210427", "210426", "210423", "210429", "210424", "275306", "275301", "275304", "221705", "275103", "221602", "275102", "275302", "276403", "275303", "221706", "275305", "276402", "275105", "221603", "221601", "275101", "276405", "231304", "231313", "231309", "231314", "231311", "231211", "231312", "231307", "231501", "231305", "231302", "231303", "231301", "231306", "231001", "244410", "244102", "244504", "244413", "244103", "244303", "244601", "244402", "244411", "244415", "244001", "244104", "244301", "244412", "244414", "244302", "244401", "244602", "134113", "134118", "134107", "134117", "134204", "134112", "134202", "134109", "134116", "134114", "134104", "133301", "134108", "133302", "134103", "134205", "134102", "134101", "132140", "132103", "132105", "132106", "132113", "132145", "132122", "132104", "132115", "132101", "132102", "132108", "132107", "262202", "262203", "262305", "262001", "262124", "262302", "262121", "262201", "262122", "230403", "230301", "230136", "230001", "230302", "230141", "230135", "230132", "230144", "230503", "230405", "230131", "230125", "230124", "230138", "230129", "230126", "230127", "230130", "230502", "230404", "230133", "230142", "230121", "229408", "230202", "230143", "230134", "230204", "230304", "230201", "230002", "230402", "230401", "230137", "230128", "230139", "230306", "229410", "230501", "229303", "229308", "229206", "229404", "229130", "229311", "229209", "229120", "229001", "229126", "229201", "229302", "229203", "229305", "229212", "229103", "229801", "229128", "229310", "229204", "229304", "229309", "229316", "229125", "229135", "229216", "229405", "229402", "229802", "229127", "229124", "229301", "229205", "229123", "229215", "229211", "229129", "229401", "229202", "229307", "229121", "229122", "229210", "229207", "229010", "229208", "229406", "229306", "244928", "244925", "244926", "244924", "244927", "244923", "244901", "244701", "244922", "244921", "124113", "124303", "124112", "124501", "124022", "124010", "124401", "124111", "124001", "124513", "124412", "124514", "124411", "124021", "124406", "124141", "247122", "247451", "247121", "247343", "247231", "247662", "247129", "247669", "247340", "247002", "247554", "247551", "247232", "247001", "247342", "247120", "247341", "247452", "247453", "272126", "272173", "272170", "272270", "272271", "272165", "272199", "272176", "272172", "272164", "272162", "221404", "221308", "221304", "221406", "221314", "221402", "221310", "221303", "221401", "221301", "221409", "221309", "242001", "242301", "242303", "242220", "242123", "242221", "242042", "242405", "242127", "242407", "242306", "242226", "242223", "242406", "242401", "242307", "242305", "271805", "271821", "271845", "271831", "271835", "271840", "271803", "272206", "272189", "272208", "272154", "272148", "272193", "272207", "272129", "272152", "272202", "272205", "272204", "272203", "272153", "272192", "272195", "125110", "125060", "125103", "125102", "125056", "125078", "125055", "125077", "125201", "125076", "125075", "125058", "125104", "125101", "125054", "261303", "261202", "261201", "261145", "261141", "261131", "261203", "261206", "261404", "261301", "261208", "261125", "261205", "261402", "261121", "261405", "261401", "261136", "261001", "261403", "261204", "261207", "261135", "261151", "261302", "231210", "231208", "231222", "231216", "231217", "231215", "231220", "231206", "231205", "231212", "231224", "231218", "231225", "231209", "231213", "231223", "231207", "231226", "231219", "231221", "227413", "227809", "228161", "227409", "227806", "227406", "227812", "228132", "227805", "228141", "227814", "228151", "227407", "222303", "228131", "228133", "228171", "228120", "227412", "228119", "227405", "227304", "227411", "228145", "228159", "227807", "222302", "227811", "228001", "228155", "227817", "227808", "227408", "228121", "228142", "227816", "222301", "227801", "228118", "227813", "228125", "227815", "209861", "209866", "209821", "209867", "209859", "209825", "209881", "209864", "209827", "209831", "209868", "209863", "209871", "209801", "209870", "209865", "209841", "209862", "209869", "221302", "221005", "221004", "221208", "221105", "221104", "221204", "221007", "232106", "221101", "221405", "221311", "221206", "221112", "221003", "232111", "221006", "232103", "221002", "221115", "221306", "221008", "221107", "232107", "221307", "221202", "221207", "221201", "232102", "221011", "221108", "221103", "221010", "221106", "221110", "221001", "221403", "232110", "221313", "221305", "221116", "135021", "133206", "133204", "135102", "135103", "135003", "133103", "135004", "135001", "135101", "135002", "135106", "135133", "248179", "262405", "247661", "249175", "263631", "263641", "246482", "262502", "246453", "246279", "249130", "246129", "248196", "263621", "246486", "246164", "249151", "263128", "248121", "263659", "246276", "246441", "246193", "248171", "247666", "263149", "246179", "262542", "246487", "249306", "262530", "263656", "249403", "248197", "246169", "246165", "246277", "249124", "246125", "248122", "263601", "263667", "244717", "263632", "263134", "263624", "249186", "263137", "262532", "246121", "249410", "248002", "246483", "263132", "263001", "246171", "248009", "246427", "262580", "262552", "246474", "248143", "262308", "249131", "246173", "263628", "263139", "249145", "249180", "249126", "263633", "263655", "263651", "249194", "246428", "263150", "247665", "246146", "263634", "246488", "263679", "262501", "249304", "246142", "248008", "263158", "263138", "246162", "262521", "246174", "246449", "262523", "249123", "263126", "246444", "263151", "246155", "263140", "248005", "247670", "246471", "246149", "263622", "248140", "246448", "262541", "263135", "263626", "263136", "246161", "263157", "249137", "246442", "249201", "249152", "246445", "248198", "263652", "263642", "246421", "249199", "263643", "244713", "263620", "263680", "262520", "248011", "262524", "263619", "262555", "246163", "249001", "246144", "248199", "248125", "246172", "262561", "263660", "263630", "248007", "249407", "263678", "247656", "246159", "263625", "249402", "248001", "262540", "263145", "262553", "246419", "246167", "246124", "246130", "249171", "248146", "263665", "248159", "263653", "249135", "262310", "249128", "246426", "262554", "246472", "248195", "262547", "263160", "263664", "246148", "244712", "246177", "246431", "263640", "248124", "247668", "246401", "246175", "262551", "246123", "249401", "263159", "263638", "262543", "263676", "263153", "246425", "262576", "262309", "249141", "249193", "246001", "246141", "247671", "246446", "249202", "262527", "247663", "246131", "249146", "248015", "246481", "246473", "247664", "262528", "263637", "263156", "248145", "249165", "262545", "249301", "249196", "262533", "249122", "248158", "262522", "246194", "247667", "263658", "263635", "249408", "248165", "263663", "246127", "249155", "249205", "246475", "263639", "249404", "262311", "248006", "262550", "262525", "246422", "246275", "246147", "246424", "249411", "249132", "249192", "246285", "246429", "248012", "263629", "263661", "246440", "263646", "262526", "249195", "246176", "263645", "263148", "246495", "249181", "249405", "244715", "246439", "246113", "263152", "262531", "262534", "246435", "249302", "244716", "248013", "246128", "246278", "248123", "263623", "249161", "262402", "249203", "248014", "262544", "262529", "249185", "248003", "246166", "249125", "262546", "246469", "246443", "262401", "249204", "249121", "263636", "248142", "263127", "246455", "751034", "834012"], "exlcude_cities": []}, "groups_name": "Sangeeta - North"}]$$), $$Open - Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$open$$->$$remarks$$, $$Open - Attachments$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$open$$->$$attachments$$->$$general$$), $$Open - Updated on$$ ,TO_CHAR((txn_open.trnstn_date::timestamp AT TIME ZONE $$utc$$ AT TIME ZONE 'Asia/Kolkata'), $$YYYY-MM-DD hh:miPM$$), $$Starting Journey - Photo$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$JBv9ldkc$$->$$camera_files$$->$$1e1713d5-5fa6-49a4-bbd7-a530fa16af1a$$), $$Starting Journey - Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$JBv9ldkc$$->$$remarks$$, $$Starting Journey - Attachments$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$JBv9ldkc$$->$$attachments$$->$$general$$), $$Starting Journey - Updated on$$ ,TO_CHAR((txn_JBv9ldkc.trnstn_date::timestamp AT TIME ZONE $$utc$$ AT TIME ZONE 'Asia/Kolkata'), $$YYYY-MM-DD hh:miPM$$), $$Reached Site - Photo$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$PCQQqZmK$$->$$camera_files$$->$$d0446494-0029-4736-944e-acae24802774$$), $$Reached Site - Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$PCQQqZmK$$->$$remarks$$, $$Reached Site - Attachments$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$PCQQqZmK$$->$$attachments$$->$$general$$), $$Reached Site - Updated on$$ ,TO_CHAR((txn_PCQQqZmK.trnstn_date::timestamp AT TIME ZONE $$utc$$ AT TIME ZONE 'Asia/Kolkata'), $$YYYY-MM-DD hh:miPM$$), $$Job Started - Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$RZMKG1om$$->$$remarks$$, $$Job Started - Attachments$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$RZMKG1om$$->$$attachments$$->$$general$$), $$Job Started - Updated on$$ ,TO_CHAR((txn_RZMKG1om.trnstn_date::timestamp AT TIME ZONE $$utc$$ AT TIME ZONE 'Asia/Kolkata'), $$YYYY-MM-DD hh:miPM$$), $$Customer Cancelled - Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$sExp8Twt$$->$$remarks$$, $$Customer Cancelled - Attachments$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$sExp8Twt$$->$$attachments$$->$$general$$), $$Customer Cancelled - Updated on$$ ,TO_CHAR((txn_sExp8Twt.trnstn_date::timestamp AT TIME ZONE $$utc$$ AT TIME ZONE 'Asia/Kolkata'), $$YYYY-MM-DD hh:miPM$$), $$Job Completed - Type of Service$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Product Complaint", "value": "343701a5-4275-4f47-8824-7ca4fd165098"}, {"label": "Installation Guidance", "value": "8387acf5-ff04-4a49-b686-01545e9004f5"}, {"label": "Damage Material Inspection", "value": "5b9aaf4d-c9b3-4133-baa9-89d7c87563c8"}, {"label": "Fresh Material Inspection", "value": "2f314692-e4d0-4d93-aa36-53b0f157ea3a"}, {"label": "APC", "value": "306822de-71ba-4a1d-8449-7cf32b0b088d"}, {"label": "Dealer Display Inspection", "value": "5f8198a5-36cf-47de-8600-5ae7eb15cafd"}, {"label": "Training", "value": "73ddbdf6-a946-47e3-b6cf-0a70f707bd98"}, {"label": "Exhibition", "value": "99ac546c-2031-4b93-9f65-ce4250832cd2"}, {"label": "Damaged in new box", "value": "5104e532-38e2-488d-ad07-ca18d4399f89"}, {"label": "Product Missing in new box", "value": "14c5fd7f-c880-4344-b8f2-03cd0ae68d13"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$a8371241-50d3-4e69-a943-0ba8b45e74ba$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$a8371241-50d3-4e69-a943-0ba8b45e74ba$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$a8371241-50d3-4e69-a943-0ba8b45e74ba$$ )))
                                              )
)
),$$,$$), $$Job Completed - Invoice$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Attached", "value": "8ca89d76-dde8-43dc-a6ca-88cf21020d38"}, {"label": "Not Available", "value": "6f787b16-12cb-49f8-a091-47e097820b84"}, {"label": "Will Share on Whatsapp", "value": "939a99aa-5539-4489-a330-7adb70e3e5a0"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$c1b69655-9644-4edf-9b31-322b3028c920$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$c1b69655-9644-4edf-9b31-322b3028c920$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$c1b69655-9644-4edf-9b31-322b3028c920$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product Count$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "1", "value": "4fbc357a-d66e-46a7-a98b-7c54c6504c98"}, {"label": "2", "value": "59aaed06-36fc-4a74-99e6-8c9d2480f4f4"}, {"label": "3", "value": "9e2496d2-ea42-4fa4-b182-686b01dc308b"}, {"label": "4", "value": "3464bd3a-82c2-4a0a-b4ed-44dd0c035ce7"}, {"label": "5", "value": "050a6a94-b427-44a5-b6c0-985e08a3db04"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$fff71ea9-d985-44a8-815c-afb2d877f72a$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$fff71ea9-d985-44a8-815c-afb2d877f72a$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$fff71ea9-d985-44a8-815c-afb2d877f72a$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Article no.$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$c181bdfa-ed9e-4504-bbe5-ebce48c78311$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$c181bdfa-ed9e-4504-bbe5-ebce48c78311$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$c181bdfa-ed9e-4504-bbe5-ebce48c78311$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Name$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$62cd4878-8f38-4dc8-987f-8f50000fac15$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$62cd4878-8f38-4dc8-987f-8f50000fac15$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$62cd4878-8f38-4dc8-987f-8f50000fac15$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Quantity$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$017cbea1-e087-41c4-b079-9129b83b153e$$, $$Job Completed - Product 1 Quantity Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "27d72c35-8764-44e6-add6-5e7887d9935c"}, {"label": "Pcs", "value": "8e976f29-8b88-4ff9-aca8-fd8484b17e13"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$15a5e4d9-9dbe-406f-81d7-d5c2ade3558c$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$15a5e4d9-9dbe-406f-81d7-d5c2ade3558c$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$15a5e4d9-9dbe-406f-81d7-d5c2ade3558c$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Defective$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$bf0c6de2-b398-4f14-a70b-488af9b23059$$, $$Job Completed - Product 1 Defective Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "0472d2d8-e42a-446c-ac38-6c0e3f2f6cdf"}, {"label": "Pcs", "value": "4ed4d700-908f-4133-935e-57dc835469d2"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$77b98e8d-eaed-4e6e-b946-ef188d2919e7$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$77b98e8d-eaed-4e6e-b946-ef188d2919e7$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$77b98e8d-eaed-4e6e-b946-ef188d2919e7$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Material ( on which it is installed )$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Aluminium", "value": "c8540fb6-4f55-4c31-b34b-309f584c81a3"}, {"label": "Blasa Wood", "value": "cabe348e-f0fa-46e7-bf07-cb94acc3b62a"}, {"label": "Glass", "value": "9c6d1add-abd8-45d3-95dc-f6b0d8f7d269"}, {"label": "Hard fiber", "value": "c0effa9e-151d-4b6a-86dc-52753faad50e"}, {"label": "Hardwood (Beech)", "value": "cc464c0d-499d-4797-b080-f37acc411a36"}, {"label": "Plastic (PE)", "value": "1835b403-895d-41a9-bb62-994e834e659f"}, {"label": "MDF", "value": "e2073181-3266-4c9e-8bd3-ecb3f5b7706c"}, {"label": "Corian", "value": "51afc336-33ff-46ca-a8d2-8c5223b3932a"}, {"label": "Chipboard", "value": "0cc239da-2d41-49b8-8066-f898360d1605"}, {"label": "Plywood", "value": "fcdf7883-4255-4a88-bf16-abb82a5ffdb2"}, {"label": "Coreboard", "value": "fc4e3746-b7a6-47cb-a23a-147a46eded92"}, {"label": "Softwood (Spruce)", "value": "328a5cc2-51ef-40e4-a38b-38ad09596888"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$5cafb180-daa6-4db6-8a9a-58c58b866785$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5cafb180-daa6-4db6-8a9a-58c58b866785$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5cafb180-daa6-4db6-8a9a-58c58b866785$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Thickness (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$c52016d5-c0b5-41cc-af43-7f403e317187$$, $$Job Completed - Product 1 Width (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$04bd151b-6810-4763-b426-0acb0d8215f8$$, $$Job Completed - Product 1 Depth (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$6820a630-8ae8-4cda-abcf-c6fb36985a38$$, $$Job Completed - Product 1 Height (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$bf0c7baf-e3e3-447e-9677-788db1cab732$$, $$Job Completed - Product 1 Weight (kg)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e15706cc-ef5d-4186-9197-db6cffb7f8ca$$, $$Job Completed - Product 1 Issue$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Functional issue", "value": "0a4bf4fe-a2a1-4a36-ab72-d480a3225f6f"}, {"label": "Damaged", "value": "fd139e32-060f-40e7-930c-8812630c9355"}, {"label": "Surface Issue", "value": "3094df87-483c-4912-ae58-c98054330823"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$14200a33-fdea-4be9-9d24-0aa6212bd24e$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$14200a33-fdea-4be9-9d24-0aa6212bd24e$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$14200a33-fdea-4be9-9d24-0aa6212bd24e$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1  Installation Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "5b4e3691-b4c8-4103-a00a-5da440eaab62"}, {"label": "No", "value": "8a5c4154-cbbb-4ee7-b593-e1be7b85ce0f"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$0ef42cd4-fd84-454a-8475-466d2b2e19e4$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$0ef42cd4-fd84-454a-8475-466d2b2e19e4$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$0ef42cd4-fd84-454a-8475-466d2b2e19e4$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Dimensions Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "cda23e2e-699c-41ae-bf53-586540aa0d11"}, {"label": "No", "value": "62c75e9f-637f-4f4b-9a1c-3a805f899301"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$6eb9fa19-a3c5-4cd4-90c0-028e8c7320e0$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$6eb9fa19-a3c5-4cd4-90c0-028e8c7320e0$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$6eb9fa19-a3c5-4cd4-90c0-028e8c7320e0$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Mishandling$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "802847b7-caba-4c11-badc-09fea076d294"}, {"label": "No", "value": "f08ba318-622b-47ea-8f40-07c4698f8935"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$5bc8d5f5-fa98-4be4-988f-2244ca6cc366$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5bc8d5f5-fa98-4be4-988f-2244ca6cc366$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5bc8d5f5-fa98-4be4-988f-2244ca6cc366$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$c20265f4-6884-42ac-ad29-f853a3d2bddc$$, $$Job Completed - Product 1 Replacement Required$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "2a3acb6d-dd35-47a2-8243-834549fc3b2d"}, {"label": "No", "value": "60c00f03-6fea-4b89-b636-4ca95006fc3b"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$fe31d83e-8bc2-4f52-b025-332f463fe4e8$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$fe31d83e-8bc2-4f52-b025-332f463fe4e8$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$fe31d83e-8bc2-4f52-b025-332f463fe4e8$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Replacement Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Full Product", "value": "1e1f6b10-5f73-4431-b888-f9d56ef4509f"}, {"label": "Spare", "value": "65b13dde-a0ff-4388-8eaa-e774e6898335"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$2f91547e-2dbf-4d06-a6ed-317dec314084$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$2f91547e-2dbf-4d06-a6ed-317dec314084$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$2f91547e-2dbf-4d06-a6ed-317dec314084$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Replacement Qty$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$798d863d-5fd0-4830-a632-a99423c920b1$$, $$Job Completed - Product 1 Replacement Qty Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "87f3dfe1-25a0-42b5-879c-42ceb64aca48"}, {"label": "Pcs", "value": "e1843c62-14bb-4e67-84b9-6875b80e4f28"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$af6142ab-d17c-4a11-bf64-07ed3e327f25$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$af6142ab-d17c-4a11-bf64-07ed3e327f25$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$af6142ab-d17c-4a11-bf64-07ed3e327f25$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Box Available$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "42422bf4-70f3-42bf-9444-491ea80cb57d"}, {"label": "No", "value": "6b7eeb0e-923d-49ba-9087-cabf6d2c224e"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$86a1ac15-ec00-4c0f-8b14-ac165f757b5b$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$86a1ac15-ec00-4c0f-8b14-ac165f757b5b$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$86a1ac15-ec00-4c0f-8b14-ac165f757b5b$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Article no.$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$f702b5c2-e08b-46df-ac86-b43a115c337c$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f702b5c2-e08b-46df-ac86-b43a115c337c$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f702b5c2-e08b-46df-ac86-b43a115c337c$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Name$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$f807d106-83e3-4318-828a-224e8832fa3a$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f807d106-83e3-4318-828a-224e8832fa3a$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f807d106-83e3-4318-828a-224e8832fa3a$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Quantity$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$34072db4-7f27-491f-93b4-809b5eb27ebd$$, $$Job Completed - Product 2 Quantity Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "1d224f48-3ea8-4e6f-b22c-18475a78ab60"}, {"label": "Pcs", "value": "fab5fed5-ad5b-4fbf-937f-f9e7f06c10cb"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$0e10b4d7-f059-4e4d-b45e-9c8f6ab591b7$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$0e10b4d7-f059-4e4d-b45e-9c8f6ab591b7$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$0e10b4d7-f059-4e4d-b45e-9c8f6ab591b7$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Defective$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1c8d6c6d-6c3a-4326-80e6-571606cddff9$$, $$Job Completed - Product 2 Defective Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "a6d0d2c5-cab4-4616-bd79-20e9e568cd35"}, {"label": "Pcs", "value": "9bf7cbd6-5436-49c2-a191-45e55b9c5bf9"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$926e1a0b-a426-4c80-b255-ca4016cddc47$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$926e1a0b-a426-4c80-b255-ca4016cddc47$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$926e1a0b-a426-4c80-b255-ca4016cddc47$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Material ( on which it is installed )$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Aluminium", "value": "c8540fb6-4f55-4c31-b34b-309f584c81a3"}, {"label": "Blasa Wood", "value": "cabe348e-f0fa-46e7-bf07-cb94acc3b62a"}, {"label": "Glass", "value": "9c6d1add-abd8-45d3-95dc-f6b0d8f7d269"}, {"label": "Hard fiber", "value": "c0effa9e-151d-4b6a-86dc-52753faad50e"}, {"label": "Hardwood (Beech)", "value": "cc464c0d-499d-4797-b080-f37acc411a36"}, {"label": "Plastic (PE)", "value": "1835b403-895d-41a9-bb62-994e834e659f"}, {"label": "MDF", "value": "e2073181-3266-4c9e-8bd3-ecb3f5b7706c"}, {"label": "Corian", "value": "51afc336-33ff-46ca-a8d2-8c5223b3932a"}, {"label": "Chipboard", "value": "0cc239da-2d41-49b8-8066-f898360d1605"}, {"label": "Plywood", "value": "fcdf7883-4255-4a88-bf16-abb82a5ffdb2"}, {"label": "Coreboard", "value": "fc4e3746-b7a6-47cb-a23a-147a46eded92"}, {"label": "Softwood (Spruce)", "value": "328a5cc2-51ef-40e4-a38b-38ad09596888"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$1e369b43-8b7b-4fa3-ba02-8505fc89a6f5$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1e369b43-8b7b-4fa3-ba02-8505fc89a6f5$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1e369b43-8b7b-4fa3-ba02-8505fc89a6f5$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Thickness (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$4e60e83b-070b-44ba-b4ad-8024f21d5fbf$$, $$Job Completed - Product 2 Width (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$51170951-f490-4f24-9b87-038b3078f3d7$$, $$Job Completed - Product 2 Depth (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$68da7262-dcd5-4e13-aed1-5d355e929fc8$$, $$Job Completed - Product 2 Height (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e3880992-d23f-4784-bc46-288a49d26f46$$, $$Job Completed - Product 2 Weight (kg)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$14bb243a-fe0c-4f43-91d6-7444146a8594$$, $$Job Completed - Product 2 Issue$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Functional Issue", "value": "d2445ba7-40df-4095-a311-02157ac39bf4"}, {"label": "Damaged", "value": "ba0235f7-ea47-4f74-ba31-e3b2a9363373"}, {"label": "Surface Issue", "value": "6401a9ef-9601-4695-bf98-a25379d1738f"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$bd3d4b8c-23a7-4aa8-8bb3-7435c4cd1650$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$bd3d4b8c-23a7-4aa8-8bb3-7435c4cd1650$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$bd3d4b8c-23a7-4aa8-8bb3-7435c4cd1650$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Installation Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "a510d374-aefb-40e2-b6ff-ac8fb91fc55a"}, {"label": "No", "value": "534f4c4f-d03b-4a79-98c1-75ccaa62d244"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$e17ddabc-d6ba-435c-be00-b13491c19c9d$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e17ddabc-d6ba-435c-be00-b13491c19c9d$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e17ddabc-d6ba-435c-be00-b13491c19c9d$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Dimensions Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "09a4b057-f142-48ef-8219-7da1973c5ba7"}, {"label": "No", "value": "3f0d01c9-285c-400d-a265-59a1d719a2ba"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$3938071d-b46f-4135-b808-36182b592d55$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$3938071d-b46f-4135-b808-36182b592d55$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$3938071d-b46f-4135-b808-36182b592d55$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Mishandling$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "296b5323-1c22-4b58-aaa5-e6a65c077633"}, {"label": "No", "value": "79548c5f-6f59-48db-91a3-c6868f392cba"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$5ea8dd26-f4aa-43a6-899a-13a7ae370baa$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5ea8dd26-f4aa-43a6-899a-13a7ae370baa$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5ea8dd26-f4aa-43a6-899a-13a7ae370baa$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$4576462b-b2ba-48dd-a0a3-e1623dddac10$$, $$Job Completed - Product 2 Replacement Required$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "abcd420a-55af-4cd5-8ffc-2699e82c7fc1"}, {"label": "No", "value": "4693ab06-a319-4c7f-92db-000617f6bf2f"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$91263581-fd20-4762-ad18-70130a06e0be$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$91263581-fd20-4762-ad18-70130a06e0be$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$91263581-fd20-4762-ad18-70130a06e0be$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Replacement Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Full Product", "value": "1e1fd460-3668-4471-88f3-e5f0590bdf8a"}, {"label": "Spare", "value": "b0088357-2948-43c1-a620-a05b9b85b8fa"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$9618c723-0596-4691-9d9f-5fb024db430b$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$9618c723-0596-4691-9d9f-5fb024db430b$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$9618c723-0596-4691-9d9f-5fb024db430b$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Replacement Qty$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$7920e605-89d8-470a-a96d-12c90eaa0ef2$$, $$Job Completed - Product 2 Replacement Qty Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "76c3e9f2-1657-4772-86ea-ba32d8776fff"}, {"label": "Pcs", "value": "697ecbed-26b8-423f-aa10-4b9da24c0fa9"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$0724b36d-0900-45a1-bbbb-613c9f5cadac$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$0724b36d-0900-45a1-bbbb-613c9f5cadac$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$0724b36d-0900-45a1-bbbb-613c9f5cadac$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 2 Box Available$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "a7a846e2-460f-452a-ba06-328f1f8bd0ad"}, {"label": "No", "value": "76e92ae2-f4ca-4c5c-ac61-7ff2e3d4665b"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$98492da4-02e8-4ae8-aa0c-e25649781e0f$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$98492da4-02e8-4ae8-aa0c-e25649781e0f$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$98492da4-02e8-4ae8-aa0c-e25649781e0f$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Article no.$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$f5d0a739-13ea-4853-aee2-bd3cdcfa9a31$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f5d0a739-13ea-4853-aee2-bd3cdcfa9a31$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f5d0a739-13ea-4853-aee2-bd3cdcfa9a31$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Name$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$27e29c52-ac46-4989-96ed-cd31245941dd$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$27e29c52-ac46-4989-96ed-cd31245941dd$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$27e29c52-ac46-4989-96ed-cd31245941dd$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Quantity$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$3571a8f8-bb95-4e9e-836a-2fc728bf8d9f$$, $$Job Completed - Product 3 Quantity Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "fc142964-6084-488a-ae3c-1dbf35fd2423"}, {"label": "Pcs", "value": "ed9ac87f-a0fd-4742-a259-f87d636ff124"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$f2b7322d-9c9a-4e64-bd79-9afd20004b1a$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f2b7322d-9c9a-4e64-bd79-9afd20004b1a$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f2b7322d-9c9a-4e64-bd79-9afd20004b1a$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Defective$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$d8c34991-2a1a-4aab-9b49-028b81c716e7$$, $$Job Completed - Product 3 Defective Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "5a9cbe03-7c69-42a8-bfe0-bad085e0a0e7"}, {"label": "Pcs", "value": "a4232409-00c0-421c-be78-fa6c3131275a"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$63f7bde9-ba9e-4a87-9e74-d98dcdf69e99$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$63f7bde9-ba9e-4a87-9e74-d98dcdf69e99$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$63f7bde9-ba9e-4a87-9e74-d98dcdf69e99$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Material ( on which it is installed )$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Aluminium", "value": "c8540fb6-4f55-4c31-b34b-309f584c81a3"}, {"label": "Blasa Wood", "value": "cabe348e-f0fa-46e7-bf07-cb94acc3b62a"}, {"label": "Glass", "value": "9c6d1add-abd8-45d3-95dc-f6b0d8f7d269"}, {"label": "Hard fiber", "value": "c0effa9e-151d-4b6a-86dc-52753faad50e"}, {"label": "Hardwood (Beech)", "value": "cc464c0d-499d-4797-b080-f37acc411a36"}, {"label": "Plastic (PE)", "value": "1835b403-895d-41a9-bb62-994e834e659f"}, {"label": "MDF", "value": "e2073181-3266-4c9e-8bd3-ecb3f5b7706c"}, {"label": "Corian", "value": "51afc336-33ff-46ca-a8d2-8c5223b3932a"}, {"label": "Chipboard", "value": "0cc239da-2d41-49b8-8066-f898360d1605"}, {"label": "Plywood", "value": "fcdf7883-4255-4a88-bf16-abb82a5ffdb2"}, {"label": "Coreboard", "value": "fc4e3746-b7a6-47cb-a23a-147a46eded92"}, {"label": "Softwood (Spruce)", "value": "328a5cc2-51ef-40e4-a38b-38ad09596888"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$a63eb52f-f908-4621-9687-3e9b4589cb9e$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$a63eb52f-f908-4621-9687-3e9b4589cb9e$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$a63eb52f-f908-4621-9687-3e9b4589cb9e$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Thickness (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$97a72eb0-0cdb-4dde-b017-2c3dfe54dfc6$$, $$Job Completed - Product 3 Width (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f3e5b01c-ae84-437b-ab14-96e555e262c6$$, $$Job Completed - Product 3 Depth (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$fcdc0f1b-47ba-4ebb-9a10-aec798248481$$, $$Job Completed - Product 3 Height (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f8a3d941-6b8f-418a-aa88-a5b395e0564e$$, $$Job Completed - Product 3 Weight (kg)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$b8632d53-fd4e-4329-9c56-e73d992855aa$$, $$Job Completed - Product 3 Issue$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Functional Issue", "value": "5b4e2002-3ec2-40cd-b5dc-e21e3599da67"}, {"label": "Damaged", "value": "885d0c41-99d7-4304-b9c2-3e64efd809c5"}, {"label": "Surface Issue", "value": "20debf9e-823b-42f7-94f9-07851aa24a6d"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$d8d39927-60b2-4f3a-8d39-9a03b82933d6$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$d8d39927-60b2-4f3a-8d39-9a03b82933d6$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$d8d39927-60b2-4f3a-8d39-9a03b82933d6$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Installation Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "d96858bc-2f73-4ecb-863c-0ff32f7bc1d2"}, {"label": "No", "value": "e7c32c5b-a291-4b74-9946-1870b7e17b54"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$df887367-4c5d-43be-8277-233d153efcef$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$df887367-4c5d-43be-8277-233d153efcef$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$df887367-4c5d-43be-8277-233d153efcef$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Dimensions Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "9b9585e1-d554-416e-85c4-0bf6c8f45a7d"}, {"label": "No", "value": "52e47920-fdc2-4f2f-aef2-7723b97b83c7"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$9ac6d608-f63a-48c0-8776-624459ab2adf$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$9ac6d608-f63a-48c0-8776-624459ab2adf$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$9ac6d608-f63a-48c0-8776-624459ab2adf$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Mishandling$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "ae214d05-fbb4-4854-bc68-a3b03a0c05c1"}, {"label": "No", "value": "25116391-6d45-4d75-9733-d7edf8e6b483"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$ca238083-a1e2-4ed3-9b7a-458d371c6a02$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$ca238083-a1e2-4ed3-9b7a-458d371c6a02$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$ca238083-a1e2-4ed3-9b7a-458d371c6a02$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$71452fe5-6f65-4ce5-9452-7a3f3253b6d6$$, $$Job Completed - Product 3 Replacement Required$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "0982e65c-5317-4ebe-b929-b360c78a2062"}, {"label": "No", "value": "dde213a4-9b35-4e6f-816d-49ddc4c8dda4"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$88e35ea7-1680-47df-a923-eb46465dfb1e$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$88e35ea7-1680-47df-a923-eb46465dfb1e$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$88e35ea7-1680-47df-a923-eb46465dfb1e$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Replacement Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Full Product", "value": "4921493a-fc3c-43b7-a680-48c5e44c75a6"}, {"label": "Spare", "value": "05053b91-dbb7-48ca-8701-259e5f44da9e"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$bce05265-d2c8-48b4-a062-06039313dd17$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$bce05265-d2c8-48b4-a062-06039313dd17$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$bce05265-d2c8-48b4-a062-06039313dd17$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Replacement Qty$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$3ddcc5b9-3873-49d3-9545-0a07b0532d12$$, $$Job Completed - Product 3 Replacement Qty Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "3c24a0d5-36b0-4023-a316-fb0996474216"}, {"label": "Pcs", "value": "72f46bf3-f16a-43b4-ab83-1ef2500e49c8"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$b66169fb-7eb0-4fbe-a94c-fa77fe573bee$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$b66169fb-7eb0-4fbe-a94c-fa77fe573bee$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$b66169fb-7eb0-4fbe-a94c-fa77fe573bee$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 3 Box Available$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "e95bbaa3-1469-4ad1-ab3e-d7db4acacb97"}, {"label": "No", "value": "009004c3-5a16-4c66-a329-ad1f44049a4e"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$e250ce9b-4a97-4b64-b39c-ba4cbb2171bb$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e250ce9b-4a97-4b64-b39c-ba4cbb2171bb$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e250ce9b-4a97-4b64-b39c-ba4cbb2171bb$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Article no.$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$663ab6ec-8c29-4df5-9bc5-4ef92ea5fba2$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$663ab6ec-8c29-4df5-9bc5-4ef92ea5fba2$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$663ab6ec-8c29-4df5-9bc5-4ef92ea5fba2$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Name$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$5ee351fb-1d4d-4ab8-91ea-e540f056a78d$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5ee351fb-1d4d-4ab8-91ea-e540f056a78d$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5ee351fb-1d4d-4ab8-91ea-e540f056a78d$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Quantity$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$86615ee2-64ad-4727-9f6b-11f86b75273f$$, $$Job Completed - Product 4 Quantity Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "1b699d2b-e224-45a1-a552-49dd2454772f"}, {"label": "Pcs", "value": "c9481b21-d8e8-4d99-a5c0-fd267f0effed"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$d30fd935-2a81-4dc9-a6b7-e647020dafdf$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$d30fd935-2a81-4dc9-a6b7-e647020dafdf$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$d30fd935-2a81-4dc9-a6b7-e647020dafdf$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Defective$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$87986a3d-6529-4207-b3e4-70f5d3778a7a$$, $$Job Completed - Product 4 Defective Type
$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "1194031c-cd3d-46cb-ab79-2baf77db1680"}, {"label": "Pcs", "value": "64ee3496-1b25-4a42-b812-63f230fb5bf6"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$dee1acde-b665-42cf-86bb-7412168f3e27$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$dee1acde-b665-42cf-86bb-7412168f3e27$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$dee1acde-b665-42cf-86bb-7412168f3e27$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Material ( on which it is installed )$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Aluminium", "value": "c8540fb6-4f55-4c31-b34b-309f584c81a3"}, {"label": "Blasa Wood", "value": "cabe348e-f0fa-46e7-bf07-cb94acc3b62a"}, {"label": "Glass", "value": "9c6d1add-abd8-45d3-95dc-f6b0d8f7d269"}, {"label": "Hard fiber", "value": "c0effa9e-151d-4b6a-86dc-52753faad50e"}, {"label": "Hardwood (Beech)", "value": "cc464c0d-499d-4797-b080-f37acc411a36"}, {"label": "Plastic (PE)", "value": "1835b403-895d-41a9-bb62-994e834e659f"}, {"label": "MDF", "value": "e2073181-3266-4c9e-8bd3-ecb3f5b7706c"}, {"label": "Corian", "value": "51afc336-33ff-46ca-a8d2-8c5223b3932a"}, {"label": "Chipboard", "value": "0cc239da-2d41-49b8-8066-f898360d1605"}, {"label": "Plywood", "value": "fcdf7883-4255-4a88-bf16-abb82a5ffdb2"}, {"label": "Coreboard", "value": "fc4e3746-b7a6-47cb-a23a-147a46eded92"}, {"label": "Softwood (Spruce)", "value": "328a5cc2-51ef-40e4-a38b-38ad09596888"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$82f46a09-5dca-44f6-8b29-c77830627836$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$82f46a09-5dca-44f6-8b29-c77830627836$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$82f46a09-5dca-44f6-8b29-c77830627836$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Thickness (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$a95d4eac-5236-48b5-b71d-5c20851a3938$$, $$Job Completed - Product 4 Width (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$de08db8f-3f00-46a4-94b2-ba066460bc61$$, $$Job Completed - Product 4 Depth (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e77615c6-f3f3-45ec-b135-6eda9002ad99$$, $$Job Completed - Product 4 Height (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$9832fe8d-ba54-443e-ae78-758848f734ec$$, $$Job Completed - Product 4 Weight (kg)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$71ed67fb-e04b-4167-bd7b-67bf0b66d8d3$$, $$Job Completed - Product 4 Issue$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Functional Issue", "value": "c1e6acfd-cf8a-44d4-98a7-aafb9b7ac0a4"}, {"label": "Damaged", "value": "b4fc9195-9d24-4f73-b9a1-18f5ae7868d9"}, {"label": "Surface Issue", "value": "de6e20c7-4da6-4082-ae99-4a1211ac6151"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$30773a72-8052-44d7-bf29-700f5f5c5b8c$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$30773a72-8052-44d7-bf29-700f5f5c5b8c$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$30773a72-8052-44d7-bf29-700f5f5c5b8c$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Installation Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "2e91b526-e5e1-4608-bc32-a858f44f3abf"}, {"label": "No", "value": "e6d31ed3-2802-4c92-b11d-3f3cf8f05d33"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$76035e48-aee7-4c0f-9663-d2d93334f1dc$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$76035e48-aee7-4c0f-9663-d2d93334f1dc$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$76035e48-aee7-4c0f-9663-d2d93334f1dc$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Dimensions Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "4ca1c574-4492-434a-ba38-df40f43f07c4"}, {"label": "No", "value": "75735883-eb81-4792-a54f-e22694075d01"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$a682aa3a-fbee-4fc7-875b-976c57f96fc9$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$a682aa3a-fbee-4fc7-875b-976c57f96fc9$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$a682aa3a-fbee-4fc7-875b-976c57f96fc9$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Mishandling$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "234d6f22-26ac-4ad1-a822-0065597ca86d"}, {"label": "No", "value": "61955100-572d-4b37-b6bf-5ead7dbe1ff4"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$1cf6ee36-cfc7-46f7-9edf-4fab2954a918$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1cf6ee36-cfc7-46f7-9edf-4fab2954a918$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1cf6ee36-cfc7-46f7-9edf-4fab2954a918$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$04ed187b-4f61-4e42-a59e-d760165425b3$$, $$Job Completed - Product 4 Replacement Required$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "0d666bca-fae3-44d2-8038-89c348ca251f"}, {"label": "No", "value": "d3332d85-b77d-46e9-a789-e48661b9c517"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$1e119b9c-985c-4bd9-948a-cadc724af4e9$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1e119b9c-985c-4bd9-948a-cadc724af4e9$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1e119b9c-985c-4bd9-948a-cadc724af4e9$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Replacement Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Full Product", "value": "b93ad396-b6be-488d-94e5-1a6165239644"}, {"label": "Spare", "value": "f015a7a0-27bd-4784-9795-7b849ead3d7b"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$5b55ac0d-0208-479f-830c-d7a7969fe9eb$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5b55ac0d-0208-479f-830c-d7a7969fe9eb$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$5b55ac0d-0208-479f-830c-d7a7969fe9eb$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Replacement Qty$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e8725086-5141-4a3f-bb40-b8e008f04e91$$, $$Job Completed - Product 4 Replacement Qty Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "7a80e4ca-db26-42ba-9db8-8e99a2a3777c"}, {"label": "Pcs", "value": "e98e4ad5-a939-4505-82ab-154e3f23689a"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$fd0b368a-f9ed-4203-81b1-c80372c245de$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$fd0b368a-f9ed-4203-81b1-c80372c245de$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$fd0b368a-f9ed-4203-81b1-c80372c245de$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 4 Box Available$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "20ffc3bb-1896-4ff1-b96d-f86b34d35757"}, {"label": "No", "value": "e13fb8cd-cd02-4c46-a20b-d3df4d3115b0"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$33397467-e2d6-4c18-8f13-3caf4f601544$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$33397467-e2d6-4c18-8f13-3caf4f601544$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$33397467-e2d6-4c18-8f13-3caf4f601544$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Article no.$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$6176caaa-59b7-4e48-976d-ab1deda82826$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$6176caaa-59b7-4e48-976d-ab1deda82826$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$6176caaa-59b7-4e48-976d-ab1deda82826$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Name$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Option1", "value": "c5c12693-725f-4b39-81b9-6719244f2480"}, {"label": "Option2", "value": "e371172d-c76d-4728-b062-2928e87e8700"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$f78ec6cd-7e98-4ea6-b5a9-e953a76e083f$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f78ec6cd-7e98-4ea6-b5a9-e953a76e083f$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$f78ec6cd-7e98-4ea6-b5a9-e953a76e083f$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Quantity$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$3a3d61d9-533c-40a9-8808-68515381810f$$, $$Job Completed - Product 5 Quantity Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "735cb3a2-5441-417b-a32b-637595f24d3c"}, {"label": "Pcs", "value": "a31802fe-0894-4783-9a94-079def6979fc"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$777119f5-9851-4c2c-ace3-32a3975d5c2a$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$777119f5-9851-4c2c-ace3-32a3975d5c2a$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$777119f5-9851-4c2c-ace3-32a3975d5c2a$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Defective$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$4979b1b7-7dc3-4354-a47d-6101881f8e91$$, $$Job Completed - Product 5 Defective Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "99ad401c-44a2-469f-ba93-28dd8ae8b32e"}, {"label": "Pcs", "value": "5f826795-b590-420e-8370-88af3d353790"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$11372cdf-4a93-4183-8703-c545cf11ca3f$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$11372cdf-4a93-4183-8703-c545cf11ca3f$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$11372cdf-4a93-4183-8703-c545cf11ca3f$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Material ( on which it is installed )$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Aluminium", "value": "c8540fb6-4f55-4c31-b34b-309f584c81a3"}, {"label": "Blasa Wood", "value": "cabe348e-f0fa-46e7-bf07-cb94acc3b62a"}, {"label": "Glass", "value": "9c6d1add-abd8-45d3-95dc-f6b0d8f7d269"}, {"label": "Hard fiber", "value": "c0effa9e-151d-4b6a-86dc-52753faad50e"}, {"label": "Hardwood (Beech)", "value": "cc464c0d-499d-4797-b080-f37acc411a36"}, {"label": "Plastic (PE)", "value": "1835b403-895d-41a9-bb62-994e834e659f"}, {"label": "MDF", "value": "e2073181-3266-4c9e-8bd3-ecb3f5b7706c"}, {"label": "Corian", "value": "51afc336-33ff-46ca-a8d2-8c5223b3932a"}, {"label": "Chipboard", "value": "0cc239da-2d41-49b8-8066-f898360d1605"}, {"label": "Plywood", "value": "fcdf7883-4255-4a88-bf16-abb82a5ffdb2"}, {"label": "Coreboard", "value": "fc4e3746-b7a6-47cb-a23a-147a46eded92"}, {"label": "Softwood (Spruce)", "value": "328a5cc2-51ef-40e4-a38b-38ad09596888"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$dbe0f0c8-57c7-44fa-8e5e-bd2b38e7c676$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$dbe0f0c8-57c7-44fa-8e5e-bd2b38e7c676$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$dbe0f0c8-57c7-44fa-8e5e-bd2b38e7c676$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Thickness (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$4f91330f-9054-4e2b-a58c-378a1f7773e8$$, $$Job Completed - Product 5 Width (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$02f0e5ee-af12-4656-8488-b0a587f7faea$$, $$Job Completed - Product 5 Depth (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$db634bfe-fc61-44ed-bcd1-d951c26470bc$$, $$Job Completed - Product 5 Height (mm)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$131738a4-1b98-402a-ae23-41cf8221415d$$, $$Job Completed - Product 5 Weight (kg)$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$c5eafdb2-139e-4ded-931e-bf71aad45004$$, $$Job Completed - Product 5 Issue$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Functional Issue", "value": "28f2d415-bb23-48eb-8e4b-1ab5a22e5471"}, {"label": "Damaged", "value": "edac53e2-daf9-4df1-8b88-6025058d6054"}, {"label": "Surface Issue", "value": "6988a112-5f77-4c98-81f5-b4166d367d12"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$2814069a-8daa-47af-8a96-24386f9b80ff$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$2814069a-8daa-47af-8a96-24386f9b80ff$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$2814069a-8daa-47af-8a96-24386f9b80ff$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Installation Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "57ef1c25-babd-468c-954c-2ebe936b8c51"}, {"label": "No", "value": "fb9eec81-a2b1-41c6-8a1a-3a46e3f50f4f"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$9386c3a7-81ff-4397-8f59-d4ede95af12f$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$9386c3a7-81ff-4397-8f59-d4ede95af12f$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$9386c3a7-81ff-4397-8f59-d4ede95af12f$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Dimensions Correct$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "88cf207d-91c9-4a6e-9920-68f34dc4d058"}, {"label": "No", "value": "b126ce3b-654b-4650-9e46-28493ed29c04"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$1886a63d-86da-41e9-939c-36b7ed7dee8c$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1886a63d-86da-41e9-939c-36b7ed7dee8c$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1886a63d-86da-41e9-939c-36b7ed7dee8c$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Mishandling$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "ee2a8f8f-ba23-4199-8dcc-f18d09151135"}, {"label": "No", "value": "224502f0-e1c7-4fef-bf75-ddc31734c0df"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$29f9287a-ca7d-4f40-a57e-8a38e21b878d$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$29f9287a-ca7d-4f40-a57e-8a38e21b878d$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$29f9287a-ca7d-4f40-a57e-8a38e21b878d$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$de6a2ec0-2ded-41d9-9343-baa68b5e657d$$, $$Job Completed - Product 5 Replacement Required$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "17b8d9d0-8153-475d-a14f-5c9061728dba"}, {"label": "No", "value": "01c7572d-8e52-4b1f-97c4-cc5491f8af0f"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$8c11f4f4-62cf-4745-9ed2-bbea31a83881$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$8c11f4f4-62cf-4745-9ed2-bbea31a83881$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$8c11f4f4-62cf-4745-9ed2-bbea31a83881$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Replacement Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Full Product", "value": "c4c85d6c-4c55-435a-ae08-d2ab838c0695"}, {"label": "Spare", "value": "c8cca57b-9bdf-43b7-a073-b2c70a5375b2"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$05ee2de7-e3ca-4f75-8453-084773a442d8$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$05ee2de7-e3ca-4f75-8453-084773a442d8$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$05ee2de7-e3ca-4f75-8453-084773a442d8$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Replacement Qty$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$e8a47479-8afc-4887-a82b-50d3dbab5155$$, $$Job Completed - Product 5 Replacement Qty Type$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Sets", "value": "4b11d7ff-7a7a-4872-b122-f28f9cff6f51"}, {"label": "Pcs", "value": "48ac2c41-a534-4c1d-87df-6c7429e0f9e0"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$6849a778-8139-48b2-8144-2aed7c1bda7b$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$6849a778-8139-48b2-8144-2aed7c1bda7b$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$6849a778-8139-48b2-8144-2aed7c1bda7b$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 5 Box Available$$ ,array_to_string(array(
(SELECT
      option_label ->>$$label$$
        FROM
          (
            SELECT
                json_array_elements(($$[{"label": "Yes", "value": "c5a7278b-b337-4376-ac49-0852ab9c2fa1"}, {"label": "No", "value": "ee6623f7-49ee-4600-9613-b694c2260db0"}]$$)) AS option_label
          ) AS subquery
       WHERE
          subquery.option_label->>$$value$$ = sbtsk.form_data->$$update_type_data$$->$$closed$$->>$$1f6d4998-8c4d-45be-a154-517d652f3761$$
  or (
                                                 jsonb_typeof(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1f6d4998-8c4d-45be-a154-517d652f3761$$) = $$array$$
                                                 and
                                                 option_label ->>$$value$$ = ANY(array(select value from jsonb_array_elements_text(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$1f6d4998-8c4d-45be-a154-517d652f3761$$ )))
                                              )
)
),$$,$$), $$Job Completed - Product 1 Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$eb25ee46-417d-4501-9aba-65fc608372b7$$), $$Job Completed - Product 1 DN Sticker Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$ddf40ab9-6f4b-459f-9ba0-3a2ce8a3c544$$), $$Job Completed - Product 1 Complete Box Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$3ebf9223-1c05-4166-8c22-4f003ec94905$$), $$Job Completed - Product 1 Damaged/Missing part Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$bb98ff65-bae2-40b5-a9e8-841312f477ef$$), $$Job Completed - Product 1 Other Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$48008731-dbbc-48d3-ab88-770f65164bc9$$), $$Job Completed - Product 2 Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$043527f6-6f60-44eb-b763-b6fa31e487ff$$), $$Job Completed - Product 2 DN Sticker Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$0d6ccb1b-268d-4ff7-a322-053963161f49$$), $$Job Completed - Product 2 Complete Box Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$2d2fd32e-1ac4-41b1-a4d1-2f29b8fbd202$$), $$Job Completed - Product 2 Damaged/Missing part Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$a0a9565c-2917-4c01-a3e9-0433a2a8ba6a$$), $$Job Completed - Product 2 Other Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$f0c7077e-9f25-4ac5-9673-f692327a49d5$$), $$Job Completed - Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$411246f5-37cd-4f0e-990a-bdc11cec6a01$$), $$Job Completed - Product 3 Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$a5438ea4-a15e-4ac6-abb3-e0534f6ebc00$$), $$Job Completed - Product 3 DN Sticker Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$723aeff5-20c3-4b87-b31e-369c6d8dbffb$$), $$Job Completed - Product 3 Complete Box Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$43eac70e-6798-4600-b0a7-eafade636684$$), $$Job Completed - Product 3 Damaged/Missing part Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$3fbe2c9f-3c33-4286-9796-6512b1d59322$$), $$Job Completed - Product 3 Other Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$c5a0ec9a-79d0-4b5b-8231-eeeb7b33ef40$$), $$Job Completed - Product 4 Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$0baa27fb-1903-485d-aed9-85d654fb569e$$), $$Job Completed - Product 4 DN Sticker Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$fedcf568-1f5d-46d7-9107-f483ad4c6b99$$), $$Job Completed - Product 4 Complete Box Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$43642653-595c-401c-adef-98d4819f4b19$$), $$Job Completed - Product 4 Damaged/Missing part Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$10cba1cc-71e9-4a26-b3f6-3fd8c370d582$$), $$Job Completed - Product 4 Other Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$f4206a22-4033-4d18-b63e-481b1268b632$$), $$Job Completed - Product 5 Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$3ae59b63-966f-4144-a64d-0e46b23fabe6$$), $$Job Completed - Product 5 DN Sticker Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$716a615d-d42c-4ee3-9ef2-2f3ec0086528$$), $$Job Completed - Product 5 Complete Box Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$4f98a569-0daf-4cf6-9cb0-b5154b8c1efc$$), $$Job Completed - Product 5 Damaged/Missing part Image$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$ae200062-f68e-47d0-a79d-19b45582480a$$), $$Job Completed - Product 5 Other Images$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$d7563535-e919-4d71-9749-34689e01084d$$), $$Job Completed - Remarks$$ ,sbtsk.form_data->$$update_type_data$$->$$closed$$->$$remarks$$, $$Job Completed - Attachments$$ ,jsonb_array_length(sbtsk.form_data->$$update_type_data$$->$$closed$$->$$attachments$$->$$general$$), $$Job Completed - Updated on$$ ,TO_CHAR((txn_closed.trnstn_date::timestamp AT TIME ZONE $$utc$$ AT TIME ZONE 'Asia/Kolkata'), $$YYYY-MM-DD hh:miPM$$)
  )
 from cl_tx_sbtsk as sbtsk
inner join cl_tx_srvc_req as srvc_req
on srvc_req.db_id = sbtsk.srvc_req_id
  and srvc_req.is_deleted is not true
    inner join cl_cf_service_types as srvc_type
on srvc_type.service_type_id = srvc_req.srvc_type_id
inner join cl_tx_users as assigned_by
on assigned_by.usr_id = sbtsk.c_by
inner join cl_tx_users as assignee
on assignee.usr_id = any(sbtsk.assigned_to)
inner join cl_cf_sbtsk_statuses as sbtsk_status
on sbtsk_status.status_key = sbtsk.status
  and sbtsk_status.sbtsk_type_id = sbtsk.sbtsk_type
inner join cl_tx_orgs as orgs
on orgs.org_id = srvc_req.org_id

left join cl_tx_sbtsk_trnstn_log as txn_open
  on txn_open.sbtsk_id = sbtsk.db_id
 and txn_open.status_key = 'open'

left join cl_tx_sbtsk_trnstn_log as txn_JBv9ldkc
  on txn_JBv9ldkc.sbtsk_id = sbtsk.db_id
 and txn_JBv9ldkc.status_key = 'JBv9ldkc'

left join cl_tx_sbtsk_trnstn_log as txn_PCQQqZmK
  on txn_PCQQqZmK.sbtsk_id = sbtsk.db_id
 and txn_PCQQqZmK.status_key = 'PCQQqZmK'

left join cl_tx_sbtsk_trnstn_log as txn_RZMKG1om
  on txn_RZMKG1om.sbtsk_id = sbtsk.db_id
 and txn_RZMKG1om.status_key = 'RZMKG1om'

left join cl_tx_sbtsk_trnstn_log as txn_sExp8Twt
  on txn_sExp8Twt.sbtsk_id = sbtsk.db_id
 and txn_sExp8Twt.status_key = 'sExp8Twt'

left join cl_tx_sbtsk_trnstn_log as txn_closed
  on txn_closed.sbtsk_id = sbtsk.db_id
 and txn_closed.status_key = 'closed'

where sbtsk.is_deleted is not true
  and sbtsk.org_id =952
  and sbtsk.sbtsk_type =618
  and sbtsk.db_id in (3987393,3988453,3988605,3988721,3988828,3988980,3989555,3989928,3989937,3989959,3989970,3989976,3989988,3990016,3990035,3990039,3990041,3990059,3990089,3990093,3990114,3990124,3990165,3990749,3990755,3990760,3990766,3990771,3990774,3990778,3990787,3990791,3990795,3990809,3990816,3990824,3994260,3994286,3994328,3994395,3994397,3994750,3994833,3994992,3995055,3995566,3996092,3996095,3996646,3996651,3997014,4000948,4000965,4001015,4001019,4001024,4001802,4001842,4001849,4001858,4001891,4001894,4001896,4001898,4001910,4002021,4002041,4002044,4002049,4002072,4002082,4002107,4002118,4002129,4002144,4002153,4002368,4002386,4002441,4002445,4002448,4004485,4004916,4005856,4005928,4006385,4006490,4006839,4006847,4007199,4007236,4007242,4007753,4007762,4007766,4007777,4007795,4008348,4008390,4008397,4008401,4008426,4008676,4008874,4009080,4009093,4009255,4009266,4009284,4009343,4009354,4009389,4009396,4009413,4009417,4009459,4009462,4009470,4009492,4009499,4009630,4009643,4009651,4009676,4009699,4011018,4011023,4011033,4011061,4011065,4011068,4011073,4011078,4011084,4011090,4011102,4011106,4011108,4011128,4011134,4011205,4011220,4011225,4011317,4011324,4011333,4011346,4011350,4011396,4011451,4011549,4011566,4011706,4011764,4011783,4011874,4012525,4012542,4012739,4013141,4013439,4013444,4013459,4013480,4013498,4014042,4014217,4014590,4014937,4015155,4015159,4015222,4015383,4015385,4015553,4015557,4015562,4015574,4015607,4015726,4015767,4015801,4015807,4015816,4015830,4015881,4015883,4015898,4015907,4015930,4015938,4015948,4015952,4015980,4016069,4016081,4016089,4016094,4016116,4016124,4016202,4016210,4016211,4017636,4017638,4017679,4017681,4017684,4017719,4017773,4017776,4017782,4017790,4017795,4017797,4017798,4017810,4017813,4017817,4018877,4018983,4019009,4019075,4019106,4019110,4019115,4019162,4019169,4019208,4019214,4019910,4019983,4019986,4020007,4020053,4020058,4020063,4020084,4020094,4020112,4020129,4020396,4020408,4020426,4020457,4020465,4020484,4020806,4020813,4020823,4020838,4020842,4020855,4020941,4021068,4021179,4021211,4021239,4021480,4021488,4021497,4021531,4021754,4021781,4021788,4021849,4021959,4022120,4022135,4022395,4022401,4022454,4022463,4022628,4022725,4022922,4022923,4023215,4023416,4023424,4023427,4023444,4023467,4023468,4023470,4023484,4023487,4023493,4023502,4023514,4023530,4023538,4023557,4023570,4023584,4023594,4023601,4023643,4023645,4023655,4023666,4023679,4023695,4023709,4023748,4023790,4024296,4024584,4024586,4024593,4024595,4024600,4024601,4024615,4024624,4024627,4024628,4024630,4024632,4024635,4024636,4024637,4024639,4024642,4024645,4024652,4026518,4026595,4027038,4027046,4027051,4027119,4027164,4027288,4027416,4027446,4027587,4027655,4027691,4027946,4027960,4027962,4028248,4028412,4028877,4029076,4029320,4029429,4029529,4029547,4029942,4030470,4030477,4030502,4030526,4030549,4030582,4030592,4030599,4031088,4031215,4031223,4031232,4031279,4031285,4031321,4031324,4031333,4031358,4031396,4031407,4031408,4031410,4031412,4031415,4031423,4031440,4031449,4031474,4031477,4031487,4031492,4031507,4031515,4031519,4031525,4031531,4031698,4031711,4031714,4031715,4031742,4032635,4032643,4032834,4032922,4033626,4033696,4033726,4034174,4034260,4034371,4034486,4034739,4035045,4035050,4035199,4035205,4035251,4035302,4035355,4035434,4035483,4035983,4036054,4036241,4036268,4036270,4036287,4036310,4036323,4036327,4036335,4036358,4036396,4036444,4036446,4036464,4036474,4036480,4036513,4036516,4036524,4036535,4036539,4036587,4036594,4036655,4036673,4036680,4036689,4036691,4036732,4036783,4036812,4036814,4036826,4036828,4036831,4036836,4036859,4036861,4036864,4036869,4036875,4036880,4036915,4036929,4037154,4037156,4038057,4038097,4038105,4038184,4038204,4038230,4038241,4038736,4039537,4039580,4039983,4040028,4040226,4040263,4040273,4040559,4040688,4040918,4041780,4045346,4045363,4045387,4045759,4046182,4046192,4046209,4046257,4046367,4046397,4046415,4046423,4046426,4046431,4046438,4046444,4046492,4046522,4046837,4046970,4046971,4046987,4047012,4047020,4047102,4047108,4047123,4047244,4047245,4047247,4047363,4047880,4047903,4048415,4048425,4048426,4048430,4048571,4048580,4049448,4049847,4049878,4049942,4049947,4050476,4050595,4050853,4050871,4051020,4051042,4051562,4051609,4051623,4051827,4051832,4051973,4052212,4052375,4052389,4052472,4052595,4052607,4052618,4052800,4052802,4052867,4053198,4053295,4053316,4053404,4053709,4053719,4053722,4054172,4054467,4055484,4055488,4055494,4055499,4055504,4055513,4055514,4055515,4055528,4055555,4055559,4055564,4055567,4055570,4055572,4055580,4055619,4055621,4055625,4055628,4055635,4055637,4055640,4055651,4055652,4055656,4055664,4056100,4056109,4056116,4056127,4056133,4056137,4056143,4056144,4056155,4056156,4056158,4056165,4056172,4056173,4056177,4056181,4056199,4056239,4056248,4056384,4056387,4056393,4056397,4056398,4056402,4056407,4056409,4056430,4056448,4056454,4056458,4056459,4056461,4056462,4056463,4056464,4056473,4056475,4056476,4056479,4056484,4057101,4057116,4057146,4057246,4057465,4057475,4057818,4057831,4057839,4057845,4057861,4057895,4057901,4057914,4057991,4058014,4058029,4058045,4058061,4058146,4058178,4058207,4058276,4058285,4058852,4058855,4059149,4059157,4059190,4059214,4059223,4059249,4059277,4059332,4059508,4059692,4059796,4059967,4060088,4060120,4060739,4061282,4061380,4061394,4061409,4061417,4061424,4061451,4061641,4061898,4061907,4061923,4061925,4061955,4062007,4062055,4062132,4062146,4062177,4062182,4062189,4062198,4062209,4062226,4062250,4062258,4062261,4062266,4062336,4062339,4062345,4062412,4062421,4062422,4062428,4062432,4062451,4062458,4062465,4062489,4062495,4062502,4062510,4062511,4062531,4062539,4062546,4062558,4062567,4062577,4062583,4062588,4062594,4063138,4063140,4063733,4063742,4063743,4063746,4063766,4063772,4063786,4063817,4063850,4063855,4063858,4063862,4063863,4063865,4063872,4063873,4063875,4063877,4063887,4063889,4063893,4063900,4063910,4063911,4063915,4063923,4063924,4063932,4063933,4063935,4063936,4063937,4064606,4064630,4065111,4065578,4065640,4065666,4066301,4066672,4066752,4066819,4067035,4067446,4067495,4067561,4067565,4067575,4067835,4067971,4068003,4068196,4068880,4069006,4069031,4069047,4069075,4069087,4069089,4069114,4069245,4069249,4069275,4069288,4069373,4069379,4069383,4069417,4069422,4069433,4069442,4069450,4069458,4069472,4069481,4069488,4069493,4069500,4069505,4069521,4069535,4069567,4069588,4069593,4069601,4069604,4069703,4069721,4071180,4071181,4071183,4071185,4071186,4071191,4071195,4071216,4071221,4071253,4071256,4071260,4071266,4071268,4071271,4071587,4071616,4071649,4071769,4071775,4071852,4072116,4072654,4072688,4073025,4073091,4073528,4073569,4074207,4074490,4074880,4074960,4075276,4075280,4075287,4075311,4075420,4075429,4075441,4075844,4075985,4076012,4076030,4076188,4076201,4076204,4076319,4076323,4076327,4076368,4076373,4076381,4076385,4076389,4076404,4076411,4076426,4076430,4076432,4076434,4076438,4076439,4076444,4079682,4079719,4079725,4079785,4079814,4079878,4079912,4079936,4079976,4079992,4080057,4080207,4080217,4081885,4081896,4081905,4081911,4081919,4081920,4081921,4081969,4081970,4081978,4081981,4081995,4081997,4082008,4082011,4082014,4082028,4082056,4082061,4082068,4082075,4082077,4082082,4082090,4082100,4082103,4082112,4082121,4082127,4082133,4082176,4082180,4082197,4082253,4082281,4082287,4082290,4082294,4082297,4082315,4082318,4082373,4083811,4083822,4083831,4083843,4083870,4083892,4083904,4084002,4084011,4084018,4084023,4084035,4084049,4084055,4084074,4084086,4084098,4084108,4084139,4084145,4084253,4084308,4084418,4084477,4084570,4084705,4084751,4085181,4085184,4085195,4085479,4086785,4089991,4089996,4090054,4090061,4090081,4090104,4090122,4090143,4090152,4090164,4090170,4090214,4090235,4092813,4092817,4092820,4092826,4092855,4093127,4093131,4093135,4093220,4093229,4093245,4093254,4093259,4093264,4093280,4094712,4094732,4094776,4094911,4094955,4095024,4095165,4095195,4095409,4095446,4095643,4095684,4095966,4095975,4096267,4096707,4096862,4096873,4097358,4097529,4097688,4097822,4097833,4098042,4098375,4098868,4098875,4098878,4098907,4098922,4098926,4098940,4098953,4098960,4098963,4098967,4098972,4099010,4099044,4099055,4099063,4099076,4099084,4099100,4099108,4099138,4099147,4099150,4099154,4099158,4099168,4099173,4099177,4099190,4099207,4099217,4099234,4099265,4099272,4099273,4099285,4099301,4099371,4099376,4099381,4099387,4099397,4099405,4099423,4099427,4099428,4099439,4099444,4099482,4099504,4099505,4099519,4099526,4099541,4099562,4099563,4099566,4099599,4099710,4099714,4099715,4099785,4099786,4099819,4099825,4099827,4099892,4099906,4099926,4099933,4099939,4099942,4099949,4101501,4101533,4101540,4101543,4101553,4101556,4101563,4101566,4101570,4101700,4101711,4101717,4101737,4101742,4101747,4101752,4101756,4101760,4101773,4101793,4101804,4101822,4101831,4101908,4102333,4102475,4102792,4102801,4103025,4103033,4103059,4103165,4103651,4103846,4103848,4104419,4104731,4104779,4104860,4104878,4104984,4105258,4105297,4105313,4105328,4105337,4105342,4105344,4105366,4105705,4105719,4105745,4105746,4105759,4105763,4105766,4105768,4105772,4105774,4105775,4105778,4105783,4105795,4105801,4105809,4105894,4105896,4105904,4105917,4105918,4105943,4105949,4106072,4106074,4106081,4106097,4106106,4106110,4106113,4106291,4106304,4106313,4106347,4106436,4106441,4106444,4106446,4106537,4106540,4106543,4106549,4106551,4106624,4106626,4106645,4106689,4106691,4106696,4106697,4106702,4106707,4106712,4106714,4106817,4106835,4106844,4106846,4108210,4108231,4108254,4108266,4108278,4108816,4108836,4109084,4109140,4109760,4109780,4109831,4109890,4109982,4110007,4110103,4110113,4110376,4110417,4110559,4110852,4110966,4111012,4111277,4111612,4111833,4112240,4112401,4112429,4112444,4112461,4112476,4112534,4112538,4112540,4112543,4112547,4112550,4112555,4112559,4112560,4112614,4112621,4112627,4112637,4112642,4112645,4112650,4112656,4112662,4112667,4112669,4112671,4112680,4112687,4112694,4112700,4112703,4112713,4112721,4112727,4112734,4112739,4112744,4112753,4112756,4112759,4112870,4112885,4112972,4113547,4114256,4114278,4114295,4114307,4114308,4114352,4114358,4114362,4114389,4114393,4114396,4114399,4114402,4114404,4114407,4114494,4114578,4115223,4115296,4115301,4115306,4115312,4115316,4115335,4115385,4115474,4115483,4115546,4115553,4115560,4115566,4115587,4115593,4115941,4115957,4116238,4116242,4116303,4116318,4116776,4116820,4116830,4116838,4116910,4117158,4117223,4117371,4117449,4117500,4117511,4117618,4117900,4118131,4118146)
group by srvc_req.db_id,assignee.name, assigned_by.name, sbtsk_status.title, sbtsk.start_time, sbtsk.db_id, orgs.org_id, srvc_type.service_type_id, txn_open.trnstn_date, txn_JBv9ldkc.trnstn_date, txn_PCQQqZmK.trnstn_date, txn_RZMKG1om.trnstn_date, txn_sExp8Twt.trnstn_date, txn_closed.trnstn_date