const { getServiceModelFrQueue } = require('./helpers/services_helper');

const timeoutThreshold = 20; //seconds

const startTimeChecker = (spentTimeObj, done) => {
    setTimeout(() => {
        console.log('export spent time (secs)', spentTimeObj);
        // Cancel timeout if work was done within specific time
        if (spentTimeObj.spentTime > timeoutThreshold) {
            console.log('export request timedout, completing job forcefully');
            spentTimeObj.spentTime = -1;
            done(null, {});
        }
        if (spentTimeObj.spentTime >= 0) {
            spentTimeObj.spentTime = spentTimeObj.spentTime + 1;
            startTimeChecker(spentTimeObj, done);
        }
    }, 1000);
};

const performJob = async (job, done) => {
    const app = require('../../../app');
    const services_model = getServiceModelFrQueue(app, job.data.requester);
    let spentTimeObj = { spentTime: 0 };
    setTimeout(() => {
        startTimeChecker(spentTimeObj, done);
    }, 20000);

    try {
        // const resp = await projects_model.processProjectReqExportByEmail(job.data);
        const resp = await services_model.processProjectReqExportByEmail(
            job.data,
            job.id
        );
        console.log('processProjectReqExportByEmail Resp ', resp);

        // Stop the timeout checker
        spentTimeObj.spentTime = -1;

        // Wait for 20 seconds as intended, then complete the job
        setTimeout(() => {
            done(null, { response: resp });
        }, 20000);

        // Remove the duplicate done() call - this was causing immediate completion
    } catch (error) {
        console.error('Error in processProjectReqExportByEmail:', error);
        spentTimeObj.spentTime = -1;
        done(error);
    }
};

exports.default = performJob;
