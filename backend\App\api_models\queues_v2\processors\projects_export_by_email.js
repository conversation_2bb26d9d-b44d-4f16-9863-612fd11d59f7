const { getServiceModelFrQueue } = require('./helpers/services_helper');

const timeoutThreshold = 20; //seconds

const startTimeChecker = (spentTimeObj, done) => {
    setTimeout(() => {
        console.log('export spent time (secs)', spentTimeObj);
        // Cancel timeout if work was done within specific time
        if (spentTimeObj.spentTime > timeoutThreshold) {
            console.log('export request timedout, completing job forcefully');
            spentTimeObj.spentTime = -1;
            done(null, {});
        }
        if (spentTimeObj.spentTime >= 0) {
            spentTimeObj.spentTime = spentTimeObj.spentTime + 1;
            startTimeChecker(spentTimeObj, done);
        }
    }, 1000);
};

const performJob = async (job, done) => {
    const app = require('../../../app');
    const services_model = getServiceModelFrQueue(app, job.data.requester);
    let spentTimeObj = { spentTime: 0 };
    let jobCompleted = false;

    // Start the timeout checker immediately
    startTimeChecker(spentTimeObj, (error, result) => {
        if (!jobCompleted) {
            jobCompleted = true;
            done(error, result);
        }
    });

    try {
        console.log(
            'Starting WIFY_PROJECTS_EXPORT_BY_EMAIL job - will run for 20 seconds'
        );

        // Add delay at the beginning to keep job active
        console.log('Phase 1: Initial processing delay (5 seconds)...');
        await new Promise((resolve) => setTimeout(resolve, 5000));

        // const resp = await projects_model.processProjectReqExportByEmail(job.data);
        console.log('Phase 2: Running export process...');
        const resp = await services_model.processProjectReqExportByEmail(
            job.data,
            job.id
        );
        console.log('processProjectReqExportByEmail Resp ', resp);

        // Add additional delay to reach 20 seconds total
        console.log('Phase 3: Post-processing delay (15 seconds)...');
        await new Promise((resolve) => setTimeout(resolve, 15000));

        console.log('Job completed after 20 seconds');

        // Stop the timeout checker and complete the job
        spentTimeObj.spentTime = -1;

        if (!jobCompleted) {
            jobCompleted = true;
            done(null, { response: resp });
        }
    } catch (error) {
        console.error('Error in processProjectReqExportByEmail:', error);
        spentTimeObj.spentTime = -1;

        if (!jobCompleted) {
            jobCompleted = true;
            done(error);
        }
    }
};

exports.default = performJob;
