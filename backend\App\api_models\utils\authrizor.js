const HttpStatus = require('http-status-codes');
const consumer_model = require('../consumer_model');
const users_model = require('../users_model');
const brand_model = require('../brand_model');
const http_utils = require('./http_utils');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { SET_LAST_SEEN_IN_CACHE } = require('./redis_helpers');
const { getCurrentDateTime } = require('./helper');
const user_model = require('../user_model');
const saltRounds = 10;
const { JWT_SECRET } = process.env;
const getUserContextFrmReq = (req) => {
    return {
        token: req.token,
        isMobileApp: req.isMobileApp,
        uuid: req.uuid,
        hash: req.hash,
        user_details: req.user_details,
    };
};

const SetUserCtxToReq = (req, userCtxInfo) => {
    const ua = req.get('User-Agent');
    //TODO:: validate the app signature
    const APP_INDICATOR = '|TMS_APP';
    const isMobileApp = ua.indexOf(APP_INDICATOR) > 0;
    // const acc_index = req.headers['x-active-usr-index'] ?
    //     parseInt(req.headers['x-active-usr-index'], 10) :
    //     0;
    req.token = http_utils.getAuthTokenFromRequest(req);
    req.isMobileApp = isMobileApp;
    req.uuid = userCtxInfo.uuid;
    req.hash = userCtxInfo.hash;
    req.user_details = userCtxInfo.user_data;
};

const CHANGE_API_USER_TO_CHILD = (childOrgId, req, res, next) => {
    // transform the api access to child system user
    req.childOrgId = childOrgId;
    AUTH_API(req, res, next);
};

const AUTH_API = (req, res, next) => {
    let api_key = http_utils.getAuthTokenFromRequest(req);
    // console.log('AUTH_API_MIDDLEWARE : entered',api_key);
    if (api_key) {
        brand_model.database = req.app.get('db');
        brand_model.ip_addr = req.ip;
        brand_model.user_agent = req.get('User-Agent');
        brand_model
            .getSystemUsrAccountFrAPIKey(api_key, req.childOrgId)
            .then((result) => {
                // console.log('result',result);
                if (result?.resp?.uuid) {
                    let userCtxInfo = result?.resp;
                    SetUserCtxToReq(req, userCtxInfo);
                    // skip further auth checks
                    req.auth_check_done = true;
                    next();
                } else {
                    return res.sendStatus(HttpStatus.StatusCodes.UNAUTHORIZED);
                    // return res.status(result.httpStatus).send(result.resp);
                }
            })
            .catch((err) => {
                return res.sendStatus(HttpStatus.StatusCodes.UNAUTHORIZED);
            });
    } else {
        return res.sendStatus(HttpStatus.StatusCodes.UNAUTHORIZED);
    }
};

const AUTH_OWNER = (req, res, next) => {
    try {
        const auth_token = http_utils.getAuthTokenFromRequest(req);
        // console.log('AUTH_API_MIDDLEWARE : entered', auth_token);

        if (!auth_token) {
            return res
                .status(401)
                .json({ error: 'Unauthorized: No token provided' });
        }

        // Decode token (assuming you have the secret key for verification)
        const decoded = jwt.verify(auth_token, JWT_SECRET);

        if (!decoded || !decoded.user_data) {
            return res
                .status(401)
                .json({ error: 'Unauthorized: Invalid token' });
        }

        // Parse user_data if it's a string
        let user_data = decoded.user_data;
        if (typeof user_data === 'string') {
            user_data = JSON.parse(user_data);
        }

        if (!user_data.org || user_data.org.org_type !== 'ORG_TYPE_OWNER') {
            return res
                .status(403)
                .json({ error: 'Forbidden: User is not an owner' });
        }

        // Attach user data to request object for further processing
        req.user = user_data;
        next();
    } catch (error) {
        console.error('AUTH_OWNER_MIDDLEWARE error:', error);
        return res
            .status(401)
            .json({ error: 'Unauthorized: Token verification failed' });
    }
};

const AUTH_CONSUMER = (req, res, next) => {
    console.log('AUTH_CONSUMER : entered', req.query);
    // return res.sendStatus(HttpStatus.StatusCodes.UNAUTHORIZED);
    if (req.query.token) {
        consumer_model
            .getRequesterInfo(req)
            .then((userCtxInfo) => {
                // console.log("User context",userCtxInfo);
                if (userCtxInfo && userCtxInfo.isValid) {
                    SetUserCtxToReq(req, userCtxInfo);
                    // skip further auth checks
                    req.auth_check_done = true;
                    next();
                } else {
                    return res
                        .status(HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
                        .send(
                            'Seems the link has expired or feedback already submitted!!'
                        );
                }
            })
            .catch((err) => {
                // console.log(err);
                return res.sendStatus(
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            });
    } else {
        next();
    }
};

const AUTH_USER = (req, res, next) => {
    console.log('AUTH_USER : entered');
    // return res.sendStatus(HttpStatus.StatusCodes.UNAUTHORIZED);
    if (req.auth_check_done) {
        // console.log('Skipping auth because previous middleware already did it')
        next();
    } else {
        users_model
            .getRequesterInfo(req)
            .then(async (userCtxInfo) => {
                // Retrieve the flag indicating whether the cache needs to be update.
                let need_to_update_cache = userCtxInfo.need_to_update_cache;
                // If the cache update is required, proceed with the following steps.
                if (need_to_update_cache === true) {
                    // Get the user model by passing the request and context information.
                    const user_model = getUserModel(
                        req,
                        getUserContextFrmReq(req)
                    );
                    // Fetch user data based on the UUID present in the user context information.
                    let user_data = await user_model.getUserData(
                        userCtxInfo.uuid
                    );
                    // Send the user data with a PARTIAL CONTENT status.
                    return res
                        .status(HttpStatus.StatusCodes.PARTIAL_CONTENT)
                        .send(user_data.resp);
                }
                // console.log("User context",userCtxInfo);
                if (userCtxInfo && userCtxInfo.isValid) {
                    SetUserCtxToReq(req, userCtxInfo);
                    // console.log('AUTH_USER :: passing to next new req - ',req);
                    // return res.sendStatus(HttpStatus.StatusCodes.UNAUTHORIZED);
                    const uuid = userCtxInfo.uuid;
                    const hash = userCtxInfo.hash;
                    const org_id = userCtxInfo.user_data.org.id;
                    const { date, currentDateAndTime } = getCurrentDateTime();
                    if (!userCtxInfo.is_check_technician_app) {
                        if (uuid && hash && org_id && currentDateAndTime) {
                            let obj = {
                                last_seen: currentDateAndTime,
                                org_id: org_id,
                                user_id: uuid,
                                hash: hash,
                            };
                            SET_LAST_SEEN_IN_CACHE(uuid, hash, date, obj);
                        } else {
                            console.log(
                                '[LAST_SEEN_CAHCE] failed to set because of missing details ',
                                uuid,
                                hash,
                                org_id,
                                currentDateAndTime
                            );
                        }
                    }
                    next();
                } else {
                    return res.sendStatus(HttpStatus.StatusCodes.UNAUTHORIZED);
                }
            })
            .catch((err) => {
                return res.sendStatus(
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            });
    }
};

const AUTH_SYS_ADMIN = (req, res, next) => {
    console.log('AUTH_SYS_ADMIN entered');
    if (req.user) {
        console.log('AUTH_SYS_ADMIN - usrCtx ', JSON.stringify(req.user));
    } else {
        console.log('AUTH_SYS_ADMIN - no user ctx found');
    }
    const sysAdminRoleId = process.env.SYS_ADMIN_ROLE_ID;
    if (req.user && req.user.role_id && req.user.role_id === sysAdminRoleId) {
        next();
    } else {
        res.sendStatus(401);
    }
};

const checkOrgPasswordPolicy = async (req, orgId, pass) => {
    try {
        const db = req.app.get('db');
        let dbResp = await db.tms_hlpr_check_usr_password_policy(orgId, pass);
        let keyStore = dbResp[0].tms_hlpr_check_usr_password_policy;
        return keyStore;
    } catch (error) {
        throw error;
    }
};

const convertPasswordToHashInBody = async (req, res, next) => {
    try {
        let org_id = parseInt(req.user_details.org.id);
        if (req.body.batch_data && Array.isArray(req.body.batch_data)) {
            for (let user of req.body.batch_data) {
                const { password, user_confirm_pass } = user;
                if (password) {
                    const policyCheckResponse = await checkOrgPasswordPolicy(
                        req,
                        org_id,
                        password
                    );
                    if (policyCheckResponse.status == true) {
                        return res
                            .status(HttpStatus.StatusCodes.CONFLICT)
                            .send(policyCheckResponse?.message);
                    }
                    user.password = await hashPassword(user.password);
                }
                if (user_confirm_pass) {
                    user.user_confirm_pass = user.password;
                }
            }
        } else {
            const { password, user_confirm_pass } = req.body;
            if (password) {
                const policyCheckResponse = await checkOrgPasswordPolicy(
                    req,
                    org_id,
                    password
                );
                if (policyCheckResponse.status == true) {
                    return res
                        .status(HttpStatus.StatusCodes.CONFLICT)
                        .send(policyCheckResponse?.message);
                }

                req.body.password = await hashPassword(password);
            }
            if (user_confirm_pass) {
                // keep same hash for password & confirm pass
                req.body.user_confirm_pass = req.body.password;
            }
        }
    } catch (error) {
        return res.sendStatus(401);
    }
    return next();
};

const hashPassword = async (password) => {
    try {
        const hash = await bcrypt.hash(password, saltRounds);
        return hash;
    } catch (error) {
        throw new Error(`Error hashing password: ${error}`);
    }
};

const verifyPassword = async (enteredPassword, storedHash) => {
    try {
        const match = await bcrypt.compare(enteredPassword, storedHash);
        return match;
    } catch (error) {
        console.error('Error verifying password:', error);
    }
};

const getUserModel = (req, userContext) => {
    const user_model = require('../user_model');
    user_model.database = req.app.get('db');
    user_model.ip_addr = req.ip;
    user_model.user_agent = req.get('User-Agent');
    user_model.user_context = userContext;
    return user_model;
};

//const WIFY_APP_REQUEST

module.exports = {
    AUTH_USER,
    AUTH_OWNER,
    AUTH_SYS_ADMIN,
    AUTH_CONSUMER,
    AUTH_API,
    getUserContextFrmReq,
    CHANGE_API_USER_TO_CHILD,
    convertPasswordToHashInBody,
    checkOrgPasswordPolicy,
    hashPassword,
    verifyPassword,
};
